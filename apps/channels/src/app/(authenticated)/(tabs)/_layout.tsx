import { useMemo } from 'react';
import { BottomSheet } from '@shape-construction/arch-ui-native';
import { CogIcon } from '@shape-construction/arch-ui-native/src/Icons/outline';
import { ChatBubbleLeftRightIcon, PlusCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { theme } from '@shape-construction/arch-ui-native/src/theme';
import { Link, Tabs } from 'expo-router';
import { Translation, useTranslation } from 'react-i18next';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import { type EdgeInsets, useSafeAreaInsets } from 'react-native-safe-area-context';

const TAB_BAR_HEIGHT = 30;

const getStyles = (inset: EdgeInsets) =>
  StyleSheet.create({
    tabBarStyle: {
      paddingBottom: 0,
      backgroundColor: theme.colors['gray-50'],
      height: inset.bottom + TAB_BAR_HEIGHT,
      marginBottom: inset.bottom,
    },
    tabBarItemStyle: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    tabBarLabelStyle: {
      fontWeight: '500',
      fontSize: 12,
    },
  });

export const TabsLayout = () => {
  const { t } = useTranslation();
  const inset = useSafeAreaInsets();
  const styles = useMemo(() => getStyles(inset), [inset]);

  return (
    <BottomSheet.Root>
      <Tabs
        screenOptions={{
          headerShadowVisible: false,
          tabBarInactiveTintColor: theme.colors['gray-500'],
          tabBarActiveTintColor: theme.colors['indigo-500'],
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarStyle: styles.tabBarStyle,
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            tabBarLabel: t('channelList.title'),
            headerTitle: () => <Text className="text-lg leading-6 font-bold text-brand">{t('channelList.title')}</Text>,
            tabBarIcon: ChatBubbleLeftRightIcon,
            headerTitleAlign: 'left',
            headerRight: () => (
              <Translation>
                {(t) => (
                  <Link
                    asChild
                    href="/channel/new"
                    accessibilityRole="link"
                    className="mr-4"
                    aria-label={t('channelList.actions.createChannel')}
                  >
                    <TouchableOpacity>
                      <PlusCircleIcon className="w-7 h-7 text-brand-subtle" />
                    </TouchableOpacity>
                  </Link>
                )}
              </Translation>
            ),
          }}
        />

        <Tabs.Screen
          name="settings"
          options={{
            tabBarLabel: t('settings.title'),
            title: t('settings.title'),
            tabBarIcon: CogIcon,
            headerTitle: t('settings.title'),
            headerTitleAlign: 'center',
            headerShown: true,
          }}
        />
      </Tabs>
    </BottomSheet.Root>
  );
};

export default TabsLayout;
