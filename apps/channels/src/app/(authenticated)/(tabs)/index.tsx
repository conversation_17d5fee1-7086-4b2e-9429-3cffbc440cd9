import { type ComponentProps, useMemo, useState } from 'react';
import { useDebounceCallback } from '@react-hook/debounce';
import { DocumentPlusIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link, router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View } from 'react-native';
import { ChannelPreview } from 'src/components/Channel/components/ChannelPreview/ChannelPreview';
import { PreviewAvatar } from 'src/components/Channel/components/PreviewAvatar/PreviewAvatar';
import { NoResults } from 'src/components/NoResults';
import { PushNotificationPermission } from 'src/components/PushNotificationPermission';
import RenderNull from 'src/components/RenderNull';
import { SearchInput } from 'src/components/SearchInput';
import { environment } from 'src/config/environment';
import type { ChannelFilters, ChannelSort } from 'stream-chat';
import { ChannelList } from 'stream-chat-expo';

const sort: ChannelSort = [{ pinned_at: -1 }, { last_message_at: -1 }, { updated_at: -1 }];

const getChannelsFilters = (searchChannelText: string): ChannelFilters => {
  const searchFilters: ChannelFilters = searchChannelText
    ? {
        $or: [
          { name: { $autocomplete: searchChannelText } },
          { 'member.user.name': { $autocomplete: searchChannelText } },
        ],
      }
    : {};

  return {
    $or: [
      {
        type: { $in: ['group', 'team'] },
        joined: true,
        ...searchFilters,
      },
      {
        type: 'messaging',
        joined: true,
        last_message_at: { $exists: true },
        ...searchFilters,
      },
      {
        type: 'personal',
        joined: true,
        ...searchFilters,
      },
    ],
  };
};

export const ChannelListScreen = () => {
  const [searchChannelText, setSearchChannelText] = useState('');
  const { t } = useTranslation();
  const filters: ChannelFilters = useMemo(() => getChannelsFilters(searchChannelText), [searchChannelText]);

  const onSearchChange: ComponentProps<typeof SearchInput>['onChangeText'] = useDebounceCallback(
    setSearchChannelText,
    250
  );

  const onChannelSelect: ComponentProps<typeof ChannelList>['onSelect'] = async (channel) => {
    router.push({
      pathname: '/channel/[channelId]',
      params: { channelId: channel.cid },
    });
  };

  return (
    <View className="flex-1 bg-surface">
      <View className="pt-4 pb-2 px-4">
        <SearchInput autoCapitalize="none" onChangeText={onSearchChange} placeholder={t('channelList.search')} />
      </View>
      <PushNotificationPermission.Banner />
      <ChannelList
        filters={filters}
        sort={sort}
        PreviewAvatar={PreviewAvatar}
        onSelect={onChannelSelect}
        HeaderNetworkDownIndicator={RenderNull}
        Preview={ChannelPreview}
        EmptyStateIndicator={() => <NoResults />}
      />

      {environment.FEATURE_FLAG_CAPTURE_NOTES && (
        <View className="absolute flex justify-end bottom-0 right-2.5">
          <Link asChild role="link" accessibilityRole="link" href="/notes/new" className="mb-4">
            <TouchableOpacity className="px-6 py-4 bg-brand-bold rounded-full flex flex-row items-center gap-3">
              <DocumentPlusIcon className="w-6 h-6 text-brand-inverse" />
              <Text className="text-base leading-6 font-medium text-brand-inverse">
                {t('channelList.actions.captureNotes')}
              </Text>
            </TouchableOpacity>
          </Link>
        </View>
      )}
    </View>
  );
};

export default ChannelListScreen;
