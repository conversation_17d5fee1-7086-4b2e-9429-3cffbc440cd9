import { Stack } from 'expo-router';
import { ChannelListScreen } from 'src/app/(authenticated)/(tabs)';
import { environment } from 'src/config/environment';
import { renderRouter, screen } from 'src/tests/test-utils';

jest.mock('src/components/PushNotificationPermission', () => ({
  PushNotificationPermission: {
    Banner: jest.fn(() => null),
  },
}));

describe('<ChannelListScreen />', () => {
  describe('when FEATURE_FLAG_CAPTURE_NOTES is true', () => {
    beforeEach(() => {
      environment.FEATURE_FLAG_CAPTURE_NOTES = true;
    });

    afterEach(() => {
      environment.FEATURE_FLAG_CAPTURE_NOTES = false;
    });

    describe('when user clicks on capture notes', () => {
      it('navigates to /notes/new', async () => {
        const { user } = renderRouter(
          {
            '/(authenticated)/(tabs)/_layout': () => <Stack />,
            '/(authenticated)/(tabs)/index': () => <ChannelListScreen />,
            '/(authenticated)/notes/new': () => null,
          },
          { initialUrl: '/' }
        );

        await user.press(await screen.findByRole('link', { name: 'channelList.actions.captureNotes' }));

        expect(screen).toHavePathname('/notes/new');
      });
    });
  });

  describe('when FEATURE_FLAG_CAPTURE_NOTES is false', () => {
    beforeEach(() => {
      environment.FEATURE_FLAG_CAPTURE_NOTES = false;
    });

    it('does not show the capture notes button', async () => {
      renderRouter(
        {
          '/(authenticated)/(tabs)/_layout': () => <Stack />,
          '/(authenticated)/(tabs)/index': () => <ChannelListScreen />,
          '/(authenticated)/notes/new': () => null,
        },
        { initialUrl: '/' }
      );

      await screen.findByTestId('channel-list-messenger');

      expect(screen.queryByRole('link', { name: 'channelList.actions.captureNotes' })).not.toBeOnTheScreen();
    });
  });
});
