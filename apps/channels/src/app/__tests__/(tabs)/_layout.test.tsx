import { Text, View } from 'react-native';
import { TabsLayout } from 'src/app/(authenticated)/(tabs)/_layout';
import { renderRouter, screen } from 'src/tests/test-utils';

// Mock the environment module
jest.mock('src/config/environment', () => ({
  environment: {
    FEATURE_FLAG_CAPTURE_NOTES: false,
  },
}));

const RenderNull = () => null;

describe('<TabsLayout />', () => {
  describe('when settings button is pressed', () => {
    it('navigates to /settings', async () => {
      const { user } = renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/' }
      );

      await user.press(await screen.findByRole('button', { name: 'settings.title' }));

      expect(screen).toHavePathname('/settings');
    });
  });

  describe('when channels list button is pressed', () => {
    it('navigates to /', async () => {
      const { user } = renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/settings' }
      );

      await user.press(await screen.findByRole('button', { name: 'channelList.title' }));

      expect(screen).toHavePathname('/');
    });
  });

  it('renders the tabs options', async () => {
    renderRouter(
      {
        '/(tabs)/_layout': () => <TabsLayout />,
        '/(tabs)/index': () => <View data-testid="tab-screen-index" />,
        '/(tabs)/settings': () => <View data-testid="tab-screen-settings" />,
      },
      { initialUrl: '/' }
    );

    expect(await screen.findByRole('button', { name: 'channelList.title' })).toBeOnTheScreen();
    expect(await screen.findByRole('button', { name: 'settings.title' })).toBeOnTheScreen();
  });

  describe('when new channel button is pressed', () => {
    it('navigates to /channel/new', async () => {
      const { user } = renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': () => (
            <View data-testid="tab-screen-index">
              <Text>Channels list</Text>
            </View>
          ),
          '/(tabs)/settings': () => <View data-testid="tab-screen-settings" />,
        },
        { initialUrl: '/' }
      );

      await user.press(await screen.findByRole('link', { name: 'channelList.actions.createChannel' }));

      expect(screen).toHavePathname('/channel/new');
    });
  });
});
