import createMatchMedia from 'tests/create-match-media';
import { render, screen, waitFor } from 'tests/test-utils';
import { IssueQualityIndicator } from './IssueQualityIndicator';

describe('<IssueQualityIndicator />', () => {
  describe('when qualityScore is null', () => {
    it('renders nothing', () => {
      render(<IssueQualityIndicator qualityScore={null} />);

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });

  describe('when qualityScore is > 30', () => {
    it('does not render a progress bar', () => {
      render(<IssueQualityIndicator qualityScore={35} />);

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });

  describe('when qualityScore is < 30', () => {
    describe('and < 20', () => {
      it('renders a progress bar with the danger color', () => {
        const { container } = render(<IssueQualityIndicator qualityScore={15} />);

        expect(container.querySelector('circle.stroke-danger-bold')).toBeInTheDocument();
      });
    });

    describe('and >= 20', () => {
      it('renders a progress bar with the warning color', () => {
        const { container } = render(<IssueQualityIndicator qualityScore={25} />);

        expect(container.querySelector('circle.stroke-warning-bold')).toBeInTheDocument();
      });
    });
  });

  describe('when screen size is large', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(1024);
    });

    describe('when user hovers over the progress bar', () => {
      it('renders the popover', async () => {
        const { user } = render(<IssueQualityIndicator qualityScore={25} />);

        await user.hover(screen.getByRole('button'));

        expect(await screen.findByRole('dialog')).toBeInTheDocument();
      });
    });
  });

  describe('when screen size is small', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(620);
    });

    describe('when user clicks on the progress bar', () => {
      it('renders the popover', async () => {
        const { user } = render(<IssueQualityIndicator qualityScore={25} />);

        await user.click(screen.getByRole('button'));

        expect(await screen.findByRole('dialog')).toBeInTheDocument();
      });
    });
  });
});
