import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  IssueEventParametersUpdateImpactSchema,
  IssueEventSchema,
  ProjectSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { PencilSquareIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDateAndTime } from '@shape-construction/utils/DateTime';
import { parseReactElement } from '@shape-construction/utils/dom';
import { capitalCase, snakeCase } from 'change-case';
import { boldNameMessage, isEmpty } from '../feed-helper';
import { dateUpdateFields, updateFieldIconMap } from './FeedEventUpdate';

type FeedEventUpdateImpactProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  timezone: ProjectSchema['timezone'];
  parameters: IssueEventParametersUpdateImpactSchema['parameters'];
};

const formatFieldValue = (fieldName: string, fieldValue: string, timezone: ProjectSchema['timezone']): string => {
  const updateFieldValue = (dateUpdateFields as string[]).includes(fieldName)
    ? formatDateAndTime(fieldValue, timezone)
    : capitalCase(fieldValue);

  return boldNameMessage(updateFieldValue);
};

const buildParametersArray = (
  eventsMessageGetter: ReturnType<typeof useMessageGetter>,
  parameters: IssueEventParametersUpdateImpactSchema['parameters'],
  timezone: ProjectSchema['timezone']
) => {
  return Object.entries(parameters).map(([fieldName, fieldValue]) => {
    const updateFieldValue =
      typeof fieldValue === 'string' ? formatFieldValue(fieldName, fieldValue, timezone) : fieldValue;

    return eventsMessageGetter('field', {
      updateField: boldNameMessage(eventsMessageGetter(`field_${snakeCase(fieldName)}`)),
      value: updateFieldValue,
    });
  });
};

const buildParametersArrayWithChanges = (
  eventsMessageGetter: ReturnType<typeof useMessageGetter>,
  changes: NonNullable<IssueEventParametersUpdateImpactSchema['parameters']['changes']>,
  timezone: ProjectSchema['timezone']
) => {
  return Object.entries(changes).map(([fieldName, { from, to }]) => {
    const fromValue = typeof from === 'string' ? formatFieldValue(fieldName, from, timezone) : from;
    const toValue = typeof to === 'string' ? formatFieldValue(fieldName, to, timezone) : to;

    if (!isEmpty(to) && !isEmpty(from)) {
      return eventsMessageGetter('change_field', {
        updateField: boldNameMessage(eventsMessageGetter(`field_${snakeCase(fieldName)}`)),
        from: fromValue,
        to: toValue,
      });
    }

    if (from && !to) {
      return eventsMessageGetter('clear_field', {
        updateField: boldNameMessage(eventsMessageGetter(`field_${snakeCase(fieldName)}`)),
        from: fromValue,
      });
    }

    return eventsMessageGetter('update_field', {
      updateField: boldNameMessage(eventsMessageGetter(`field_${snakeCase(fieldName)}`)),
      value: toValue,
    });
  });
};

export const FeedEventUpdateImpact: React.FC<FeedEventUpdateImpactProps> = ({
  ownerName,
  date,
  timezone,
  parameters,
}) => {
  const eventsMessageGetter = useMessageGetter('issue.events.update_impact');

  const parametersArray: string[] = parameters.changes
    ? buildParametersArrayWithChanges(eventsMessageGetter, parameters.changes, timezone)
    : buildParametersArray(eventsMessageGetter, parameters, timezone);

  const updateMessage = eventsMessageGetter('message', {
    ownerName: boldNameMessage(ownerName),
    parametersCount: parametersArray.length,
    parametersArray,
  });

  const firstField = parameters.changes ? Object.keys(parameters.changes)[0] : Object.keys(parameters)[0];
  const IconComponent = updateFieldIconMap[firstField] || PencilSquareIcon;

  return (
    <FeedEventAction
      avatar={<Avatar icon={<IconComponent className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(updateMessage)}
    />
  );
};
