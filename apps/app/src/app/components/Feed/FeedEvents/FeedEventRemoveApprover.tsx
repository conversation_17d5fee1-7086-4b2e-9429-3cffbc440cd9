import React from 'react';
import { useMessage } from '@messageformat/react';
import type {
  IssueEventParametersRemoveApproverSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { UserMinusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventRemoveApproverProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  approvers: IssueEventParametersRemoveApproverSchema['parameters']['users'];
};

export const FeedEventRemoveApprover: React.FC<FeedEventRemoveApproverProps> = ({ date, ownerName, approvers }) => {
  const message = useMessage('issue.events.remove_approver', {
    ownerName: boldNameMessage(ownerName),
    approvers: approvers.map((approver) => boldNameMessage(approver.name)),
    approversCount: approvers.length,
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<UserMinusIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(message)}
    />
  );
};
