import React from 'react';
import { useMessage } from '@messageformat/react';
import type {
  IssueEventParametersArchiveSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { InboxArrowDownIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventArchiveProps = {
  ownerName: UserBasicDetailsSchema['name'];
  date: IssueEventSchema['date'];
  reason: IssueEventParametersArchiveSchema['parameters']['reason'];
};

export const FeedEventArchive: React.FC<FeedEventArchiveProps> = ({ ownerName, date, reason }) => {
  const createIssueEventMessage = useMessage('issue.events.archive', {
    ownerName: boldNameMessage(ownerName),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<InboxArrowDownIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    >
      {reason}
    </FeedEventAction>
  );
};
