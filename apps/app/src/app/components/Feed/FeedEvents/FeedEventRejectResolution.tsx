import React from 'react';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { ExclamationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventRejectResolutionProps = {
  ownerName: UserBasicDetailsSchema['name'];
  date: IssueEventSchema['date'];
};

export const FeedEventRejectResolution: React.FC<FeedEventRejectResolutionProps> = ({ ownerName, date }) => {
  const createIssueEventMessage = useMessage('issue.events.reject_resolution', {
    ownerName: boldNameMessage(ownerName),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<ExclamationCircleIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    />
  );
};
