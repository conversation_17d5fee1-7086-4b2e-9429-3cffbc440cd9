import React from 'react';
import { renderToString } from 'react-dom/server';
import { useMessage } from '@messageformat/react';
import type {
  IssueEventParametersChangeStatusSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { SHAPE, THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { CheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { ISSUE_STATE_COLORS } from 'app/constants/IssueStates';
import { capitalCase, snakeCase } from 'change-case';
import { boldNameMessage } from '../feed-helper';

type FeedEventChangeStatusProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  from: IssueEventParametersChangeStatusSchema['parameters']['from'];
  to: IssueEventParametersChangeStatusSchema['parameters']['to'];
};

const getStatusBadge = (state: string) =>
  renderToString(
    <Badge
      theme={(ISSUE_STATE_COLORS[snakeCase(state)] as THEME) || THEME.GRAY}
      label={capitalCase(state)}
      shape={SHAPE.BASIC}
    />
  );

export const FeedEventChangeStatus: React.FC<FeedEventChangeStatusProps> = ({ date, ownerName, from, to }) => {
  const typeMessage = useMessage('issue.events.change_status.type');
  const changedStatusMessage = useMessage('issue.events.change_status.message', {
    ownerName: boldNameMessage(ownerName),
    from: getStatusBadge(from),
    to: getStatusBadge(to),
    status: boldNameMessage(typeMessage),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<CheckCircleIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(changedStatusMessage)}
    />
  );
};
