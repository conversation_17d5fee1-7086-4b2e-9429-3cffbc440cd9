import React from 'react';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { ArrowUturnLeftIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventReopenProps = {
  ownerName: UserBasicDetailsSchema['name'];
  date: IssueEventSchema['date'];
};

export const FeedEventReopen: React.FC<FeedEventReopenProps> = ({ ownerName, date }) => {
  const createIssueEventMessage = useMessage('issue.events.reopen', {
    ownerName: boldNameMessage(ownerName),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<ArrowUturnLeftIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    />
  );
};
