import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  IssueEventParametersUpdateStatusStatementSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { DocumentPlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventAddStatusStatementProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  statement: IssueEventParametersUpdateStatusStatementSchema['parameters']['statement'];
};

export const FeedEventAddStatusStatement: React.FC<FeedEventAddStatusStatementProps> = ({
  date,
  ownerName,
  statement,
}) => {
  const issueEventsGetter = useMessageGetter('issue.events');
  const changedStatusMessage = issueEventsGetter('add_status_statement', {
    ownerName: boldNameMessage(ownerName),
    statusStatement: boldNameMessage(statement),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<DocumentPlusIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(changedStatusMessage)}
    />
  );
};
