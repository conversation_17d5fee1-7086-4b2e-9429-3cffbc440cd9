import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  IssueEventParametersUpdateSchema,
  IssueEventSchema,
  IssueSchema,
  ProjectSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import {
  CalendarIcon,
  CheckCircleIcon,
  CogIcon,
  EyeIcon,
  PencilSquareIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDateAndTime } from '@shape-construction/utils/DateTime';
import { parseReactElement } from '@shape-construction/utils/dom';
import { capitalCase } from 'change-case';
import { boldNameMessage, isEmpty } from '../feed-helper';

export const dateUpdateFields: Array<keyof IssueSchema> = [
  'dueDate',
  'plannedClosureDate',
  'delayStart',
  'delayFinish',
  'observedAt',
  'closedAt',
];

export const titleUpdateFields: Array<keyof IssueSchema> = ['impact', 'visibilityStatus'];

export const updateFieldIconMap: Record<string, React.ElementType> = {
  dueDate: CalendarIcon,
  plannedClosureDate: CalendarIcon,
  delayStart: CalendarIcon,
  delayFinish: CalendarIcon,
  discipline: CogIcon,
  impact: CheckCircleIcon,
  visibilityStatus: EyeIcon,
};

type FeedEventUpdateProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  parameters: IssueEventParametersUpdateSchema['parameters'];
  timezone: ProjectSchema['timezone'];
};

const formatFieldValue = (fieldName: string, fieldValue: string, timezone: ProjectSchema['timezone']): string => {
  let updateFieldValueMessage = fieldValue;
  if ((dateUpdateFields as string[]).includes(fieldName))
    updateFieldValueMessage = formatDateAndTime(fieldValue, timezone);
  else if ((titleUpdateFields as string[]).includes(fieldName)) updateFieldValueMessage = capitalCase(fieldValue);

  return boldNameMessage(updateFieldValueMessage);
};

const buildParametersArray = (
  eventsMessageGetter: ReturnType<typeof useMessageGetter>,
  parameters: IssueEventParametersUpdateSchema['parameters'],
  timezone: ProjectSchema['timezone']
) => {
  return Object.entries(parameters).map(([fieldName, value]) => {
    const updateFieldValueMessage = typeof value === 'string' ? formatFieldValue(fieldName, value, timezone) : value;

    return eventsMessageGetter('field', {
      updateField: boldNameMessage(capitalCase(fieldName)),
      value: updateFieldValueMessage,
    });
  });
};

const buildParametersArrayWithChanges = (
  eventsMessageGetter: ReturnType<typeof useMessageGetter>,
  changes: NonNullable<IssueEventParametersUpdateSchema['parameters']['changes']>,
  timezone: ProjectSchema['timezone']
) => {
  return Object.entries(changes).map(([fieldName, { from, to }]) => {
    const fromValue = typeof from === 'string' ? formatFieldValue(fieldName, from, timezone) : from;
    const toValue = typeof to === 'string' ? formatFieldValue(fieldName, to, timezone) : to;

    if (!isEmpty(from) && !isEmpty(to)) {
      return eventsMessageGetter('change_field', {
        updateField: boldNameMessage(capitalCase(fieldName)),
        from: fromValue,
        to: toValue,
      });
    }

    if (!isEmpty(from) && isEmpty(to)) {
      return eventsMessageGetter('clear_field', {
        updateField: boldNameMessage(capitalCase(fieldName)),
        from: fromValue,
      });
    }

    return eventsMessageGetter('update_field', {
      updateField: boldNameMessage(capitalCase(fieldName)),
      value: toValue,
    });
  });
};

export const FeedEventUpdate: React.FC<FeedEventUpdateProps> = ({ ownerName, date, timezone, parameters }) => {
  const updateFieldMessage = useMessageGetter('issue.events.update');
  const parametersArray = parameters.changes
    ? buildParametersArrayWithChanges(updateFieldMessage, parameters.changes, timezone)
    : buildParametersArray(updateFieldMessage, parameters, timezone);

  const updateMessage = updateFieldMessage('message', {
    ownerName: boldNameMessage(ownerName),
    parametersCount: parametersArray.length,
    parametersArray,
  });

  const firstParameter = parameters.changes ? Object.keys(parameters.changes)[0] : Object.keys(parameters)[0];
  const IconComponent = updateFieldIconMap[firstParameter] || PencilSquareIcon;

  return (
    <FeedEventAction
      avatar={<Avatar icon={<IconComponent className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(updateMessage)}
    />
  );
};
