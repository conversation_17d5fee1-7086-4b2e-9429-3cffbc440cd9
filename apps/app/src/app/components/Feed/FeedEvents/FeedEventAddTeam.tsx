import React from 'react';
import { useMessage } from '@messageformat/react';
import type {
  IssueEventParametersAddTeamSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { UserGroupIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventAddTeamPros = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  teams: IssueEventParametersAddTeamSchema['parameters']['teams'];
};

export const FeedEventAddTeam: React.FC<FeedEventAddTeamPros> = ({ date, ownerName, teams }) => {
  const message = useMessage('issue.events.add_team', {
    ownerName: boldNameMessage(ownerName),
    teams: teams.map((team) => boldNameMessage(team.displayName)),
    teamsCount: teams.length,
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<UserGroupIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(message)}
    />
  );
};
