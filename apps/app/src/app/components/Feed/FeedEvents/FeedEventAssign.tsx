import React from 'react';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { UserCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventAssignProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  recipientName: UserBasicDetailsSchema['name'];
};

export const FeedEventAssign: React.FC<FeedEventAssignProps> = ({ date, ownerName, recipientName }) => {
  const createIssueEventMessage = useMessage('issue.events.assign', {
    ownerName: boldNameMessage(ownerName),
    recipientName: boldNameMessage(recipientName),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<UserCircleIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    />
  );
};
