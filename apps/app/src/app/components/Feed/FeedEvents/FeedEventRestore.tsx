import React from 'react';
import * as ReactDOMServer from 'react-dom/server';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { InboxOutIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { parseReactElement } from '@shape-construction/utils/dom';

type FeedEventRestoreProps = {
  ownerName: UserBasicDetailsSchema['name'];
  date: IssueEventSchema['date'];
};

export const FeedEventRestore: React.FC<FeedEventRestoreProps> = ({ ownerName, date }) => {
  const ownerNameMessage = ReactDOMServer.renderToString(
    <span className="text-gray-900 font-medium">{ownerName}</span>
  );
  const createIssueEventMessage = useMessage('issue.events.restore', {
    ownerName: ownerNameMessage,
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<InboxOutIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    />
  );
};
