import React, { useMemo } from 'react';
import { useMessage } from '@messageformat/react';
import type {
  DocumentSchema,
  IssueEventParametersCreateSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { DocumentPlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { GalleryViewer } from 'app/components/Gallery/GalleryViewer';
import { boldNameMessage } from '../feed-helper';

type FeedEventCreateProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  resources: IssueEventParametersCreateSchema['resources'];
  projectId: string;
};

export const FeedEventCreate: React.FC<FeedEventCreateProps> = ({ date, resources, ownerName, projectId }) => {
  const createIssueEventMessage = useMessage('issue.events.create', {
    ownerName: boldNameMessage(ownerName),
  });
  const { images } = resources;
  const documents: (DocumentSchema | null)[] = useMemo(() => {
    return images.map((image) => {
      if (!image) return null;

      return {
        ...image.document,
        availableActions: {
          delete: false,
          edit: false,
        },
      };
    });
  }, [images]);

  return (
    <FeedEventAction
      avatar={<Avatar icon={<DocumentPlusIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(createIssueEventMessage)}
    >
      <GalleryViewer documents={documents} projectId={projectId} size="small" query="media" />
    </FeedEventAction>
  );
};
