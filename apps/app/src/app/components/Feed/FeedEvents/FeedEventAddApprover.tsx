import React from 'react';
import { useMessage } from '@messageformat/react';
import type {
  IssueEventParametersAddApproverSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { UserPlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventAddApproverProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  approvers: IssueEventParametersAddApproverSchema['parameters']['users'];
};

export const FeedEventAddApprover: React.FC<FeedEventAddApproverProps> = ({ date, ownerName, approvers }) => {
  const message = useMessage('issue.events.add_approver', {
    ownerName: boldNameMessage(ownerName),
    approvers: approvers.map((approver) => boldNameMessage(approver.name)),
    approversCount: approvers.length,
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<UserPlusIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(message)}
    />
  );
};
