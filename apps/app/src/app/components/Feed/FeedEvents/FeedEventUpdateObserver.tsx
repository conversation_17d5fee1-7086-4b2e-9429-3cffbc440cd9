import React from 'react';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { EyeIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventUpdateObserverProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  updatedObserverName?: UserBasicDetailsSchema['name'];
};

export const FeedEventUpdateObserver: React.FC<FeedEventUpdateObserverProps> = ({
  ownerName,
  date,
  updatedObserverName,
}) => {
  const observerMessage = useMessage('issue.events.update_observer.type');
  const updateMessage = useMessage('issue.events.update_observer.message', {
    ownerName: boldNameMessage(ownerName),
    observer: boldNameMessage(observerMessage),
    // @ts-expect-error Type 'undefined' is not assignable to type 'string'.ts(2345)
    updatedObserverName: boldNameMessage(updatedObserverName),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<EyeIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(updateMessage)}
    />
  );
};
