import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  IssueEventParametersDeleteStatusStatementSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { DocumentMinusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from '../feed-helper';

type FeedEventDeleteStatusStatementProps = {
  date: IssueEventSchema['date'];
  ownerName: UserBasicDetailsSchema['name'];
  statement: IssueEventParametersDeleteStatusStatementSchema['parameters']['statement'];
};

export const FeedEventDeleteStatusStatement: React.FC<FeedEventDeleteStatusStatementProps> = ({
  date,
  ownerName,
  statement,
}) => {
  const issueEventsGetter = useMessageGetter('issue.events');
  const changedStatusMessage = issueEventsGetter('delete_status_statement', {
    ownerName: boldNameMessage(ownerName),
    statusStatement: boldNameMessage(statement),
  });

  return (
    <FeedEventAction
      avatar={<Avatar icon={<DocumentMinusIcon className="text-gray-500" />} text={ownerName} />}
      date={date}
      title={parseReactElement(changedStatusMessage)}
    />
  );
};
