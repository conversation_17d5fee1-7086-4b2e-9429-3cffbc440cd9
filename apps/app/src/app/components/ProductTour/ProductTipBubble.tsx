import React, { type ReactElement, useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Link from '@shape-construction/arch-ui/src/Link';
import Popover from '@shape-construction/arch-ui/src/Popover';
import Pulse from '@shape-construction/arch-ui/src/Pulse';
import { useModal, useStableCallback } from '@shape-construction/hooks';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useShowHelpCenter } from 'app/components/HelpCenter/useShowHelpCenter';
import { useProductTipsPopover } from 'app/components/ProductTour/hooks/useProductTipsPopover';
import { useProductTourTip } from 'app/components/ProductTour/hooks/useProductTourTip';
import type { TipConfig, TourPage, TourTips } from 'app/config/productTour.config';
import { DismissConfirmationModal } from './DismissConfirmationModal';

export type ProductTipBubbleProps<Page extends TourPage> = React.ComponentProps<typeof Popover.Content> & {
  page: Page;
  tipId: TourTips<Page>;
  renderContent?: (config: TipConfig) => ReactElement;
};

export const ProductTipBubble = <Page extends TourPage>({
  page,
  tipId,
  renderContent,
  ...popoverProps
}: ProductTipBubbleProps<Page>): ReactElement => {
  const rootMessages = useMessageGetter('');
  const productTourMessages = useMessageGetter('productTour');
  const { tipState, config, dismiss, dismissAll } = useProductTourTip(page, tipId);
  const { open: isModalOpen, closeModal, openModal } = useModal(false);
  const { openProductTipsPopover } = useProductTipsPopover();
  const [open, setOpen] = useState(false);
  const { showHelpCenter } = useShowHelpCenter();

  const contentHeading = rootMessages(config.heading);
  const contentBody = useMemo(
    () => (renderContent ? renderContent(config) : parseReactElement(rootMessages(config.body))),
    [config, rootMessages, renderContent]
  );

  const openTip = useStableCallback(() => setOpen(true));

  const onClose = useStableCallback(() => setOpen(false));

  const onDismiss = useStableCallback(() => {
    onClose();
    dismiss();
  });

  const onDismissAll = useStableCallback(() => {
    onClose();
    dismissAll();
    closeModal();
    openProductTipsPopover();
  });

  if (tipState.dismissed) return <></>;

  return (
    <>
      <Popover.Root open={open}>
        <Popover.Trigger>
          <div className="w-0 -translate-x-5">
            <Pulse ariaLabel={productTourMessages('tipPulse')} onClick={openTip} />
          </div>
        </Popover.Trigger>

        <Popover.Content color="discovery" onClose={onClose} arrowPadding={20} alignOffset={0} {...popoverProps}>
          <Popover.Content.Heading>{contentHeading}</Popover.Content.Heading>
          <Popover.Content.Body>
            {contentBody}
            <p className="flex justify-start gap-1 flex-wrap pt-1">
              <span>{productTourMessages('forMoreInformation')}</span>
              <Link color="white" onClick={showHelpCenter}>
                {productTourMessages('helpCenter')}.
              </Link>
            </p>
          </Popover.Content.Body>

          <Popover.Content.Footer>
            <div className="flex gap-2">
              <Button color="white" variant="outlined" size="sm" onClick={openModal}>
                {productTourMessages('dismissAllCTA')}
              </Button>
              <Button color="discovery" variant="outlined" size="sm" onClick={onDismiss}>
                {productTourMessages('dismissCTA')}
              </Button>
            </div>
          </Popover.Content.Footer>
        </Popover.Content>
      </Popover.Root>

      <DismissConfirmationModal open={isModalOpen} dismissAll={onDismissAll} onClose={closeModal} />
    </>
  );
};
