import React, { type HTMLAttributes } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { DevicePhoneMobileIcon, HandThumbUpIcon, UserCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import SplitScreen from '@shape-construction/arch-ui/src/SplitScreen';
import { parseReactElement } from '@shape-construction/utils/dom';
import QRCorners from 'app/components/UI/Icons/QRCorners';
import { environment } from 'app/config/environment';
import { useChannelInviteAuth } from 'app/hooks/useChannelInviteAuth';

type StepProps = HTMLAttributes<HTMLDivElement> & {
  icon: React.ReactNode;
  title: string;
  description?: string;
};

const Step: React.FC<StepProps> = ({ icon, title, description, children }) => {
  return (
    <div className="flex flex-row gap-4">
      <div className="flex flex-col">
        <div className="bg-brand-subtlest rounded-full p-3">{icon}</div>
      </div>
      <div className="flex flex-col gap-2">
        <div className="font-semibold text-lg leading-6">{title}</div>
        <div className="font-normal text-sm leading-5 text">
          {description ? parseReactElement(description) : children}
        </div>
      </div>
    </div>
  );
};

const GuideSteps: React.FC = () => {
  const messages = useMessageGetter('channels.channelInvitationGuide');

  return (
    <div className="flex flex-col gap-6">
      <Step
        icon={<UserCircleIcon className="h-10 w-10 text-brand" />}
        title={messages('step1.title')}
        description={messages('step1.description')}
      />
      <Step
        icon={<HandThumbUpIcon className="h-10 w-10 text-brand" />}
        title={messages('step2.title')}
        description={messages('step2.description')}
      />
      <Step icon={<DevicePhoneMobileIcon className="h-10 w-10 text-brand" />} title={messages('step3.title')}>
        <div className="flex flex-col gap-2">
          <div className="flex flex-row gap-2">
            <a href={environment.CHANNELS_PLAY_STORE_URL} target="_blank" rel="noreferrer">
              <img
                alt="google play"
                src="/images/store/googleplay.png"
                className="h-auto w-auto max-h-8"
                aria-label="playstore button"
              />
            </a>
            <a href={environment.CHANNELS_APP_STORE_URL} target="_blank" rel="noreferrer">
              <img
                alt="app store"
                src="/images/store/appstore.png"
                className="h-auto w-auto max-h-8"
                aria-label="appstore button"
              />
            </a>
          </div>
          <div>{parseReactElement(messages('step3.description'))}</div>
          <div className="relative flex items-center justify-center h-32 w-32">
            <QRCorners className="h-auto w-auto" />
            <img
              alt="qrcode install"
              src="/images/store/qr-install.png"
              className="h-28 w-28 absolute m-auto"
              aria-label="download qr-image"
            />
          </div>
        </div>
      </Step>
    </div>
  );
};

export const ChannelRightPanel = () => {
  const { teamJoinToken } = useChannelInviteAuth();
  const messages = useMessageGetter('channels.channelInvitationGuide');

  const joinTeamDescription = messages('joinTeamDescription', {
    teamName: teamJoinToken?.teamName,
  });

  return (
    <SplitScreen.RightPanel className="bg-neutral-white md:flex-row block w-auto" data-testid="channel-right-panel">
      <div
        id="channel-invitation-instruction"
        className="flex flex-col px-4 py-6 gap-6 items-center justify-center m-auto md:h-screen md:w-4/6"
      >
        <div className="flex flex-col gap-2 justify-center items-center">
          <div className="text-xl font-bold leading-7 text-center">{messages('title')}</div>
          {teamJoinToken?.active && (
            <div className="text-base font-normal leading-6 text-center" aria-label={teamJoinToken?.teamName}>
              {joinTeamDescription}
            </div>
          )}
        </div>
        <GuideSteps />
        <Divider orientation="horizontal" variant="middle" />

        <div className="text-center flex flex-row gap-1 text-sm justify-center">
          {messages('helpText')}
          <a
            href={messages('faqUrl')}
            aria-label="faq link"
            target="_blank"
            rel="noreferrer"
            className="font-medium text-indigo-500 hover:text-indigo-400"
          >
            {messages('faq')}
          </a>
          {messages('or')}
          <a
            href={`mailto:${environment.SUPPORT_EMAIL}`}
            aria-label="contact suport mail"
            target="_blank"
            rel="noreferrer"
            className="font-medium text-indigo-500 hover:text-indigo-400"
          >
            {messages('contactSupport')}
          </a>
        </div>
      </div>
    </SplitScreen.RightPanel>
  );
};
