import React from 'react';
import { useMessage } from '@messageformat/react';
import Alert from '@shape-construction/arch-ui/src/Alert';
import { getOffsetFromTimezone } from '@shape-construction/utils/DateTime';
import { safeHTMLToReact } from '@shape-construction/utils/dom';

type AlertTimeZoneProps = {
  timeZone: string;
};

export const AlertTimeZone = ({ timeZone }: AlertTimeZoneProps) => {
  const timeZoneInfoMessage = useMessage('dateTime.timezoneInfo', {
    timezone: `${timeZone} ${timeZone ? getOffsetFromTimezone(timeZone) : ''}`,
  });
  const timeZoneInfo = safeHTMLToReact(timeZoneInfoMessage, {
    sanitizerOptions: {
      ALLOWED_TAGS: ['b'],
    },
  });

  if (!timeZone) return null;

  return (
    <Alert color="primary" emphasis="minimal" justifyContent="start">
      <Alert.Message>{timeZoneInfo}</Alert.Message>
    </Alert>
  );
};
