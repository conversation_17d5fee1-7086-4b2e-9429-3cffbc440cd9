import React from 'react';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import type { ProgressBarActiveColor } from '@shape-construction/arch-ui/src/ProgressBar/ProgressBar';

export type QualityDetailsListProgressBarProps = {
  currentProgress: number;
  isComplete?: boolean;
  isDisabled?: boolean;
};

const progressBarColor = (isComplete: boolean, isDisabled: boolean): ProgressBarActiveColor => {
  if (isDisabled) return 'secondary';
  if (!isComplete) return 'primary';

  return 'success';
};

export const QualityDetailsListProgressBar: React.FC<QualityDetailsListProgressBarProps> = ({
  currentProgress,
  isComplete,
  isDisabled,
}) => {
  const color: ProgressBarActiveColor = progressBarColor(!!isComplete, !!isDisabled);

  return (
    <div className="pt-1 pb-2">
      <ProgressBar.Root progress={currentProgress} color={color} disabled={isDisabled} />
    </div>
  );
};
