import { render, screen } from 'tests/test-utils';
import { QualityDetailsListText } from './QualityDetailsListText';

describe('<QualityDetailsListText />', () => {
  it('renders text', async () => {
    render(<QualityDetailsListText text="Some text" />);

    expect(screen.getByRole('checkbox', { name: 'Some text' })).toBeInTheDocument();
  });

  describe('when isComplete is not set', () => {
    it('does not render check indicator', async () => {
      render(<QualityDetailsListText text="Some text" />);

      expect(screen.queryByLabelText('checked progress item')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('unchecked progress item')).not.toBeInTheDocument();
    });
  });

  describe('when isComplete is true', () => {
    it('renders checklist item as checked', async () => {
      render(<QualityDetailsListText text="Some text" isComplete />);

      expect(screen.getByRole('checkbox', { name: 'Some text', checked: true })).toBeInTheDocument();
    });
  });

  describe('when isComplete is false', () => {
    it('renders checklist item as unchecked', async () => {
      render(<QualityDetailsListText text="Some text" isComplete={false} />);

      expect(screen.getByRole('checkbox', { name: 'Some text', checked: false })).toBeInTheDocument();
    });
  });

  describe('when isDisabled is true', () => {
    it('renders checklist item as disabled', async () => {
      render(<QualityDetailsListText text="Some text" isDisabled />);

      expect(screen.getByRole('checkbox', { name: 'Some text' })).toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('when isDisabled is false', () => {
    it('renders checklist item as not disabled', async () => {
      render(<QualityDetailsListText text="Some text" isDisabled={false} />);

      expect(screen.getByRole('checkbox', { name: 'Some text' })).toHaveAttribute('aria-disabled', 'false');
    });
  });
});
