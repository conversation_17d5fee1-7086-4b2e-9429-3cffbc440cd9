import React from 'react';
import { CircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

type CheckedRowProps = { isChecked: boolean; isDisabled?: boolean };

const CheckedRow: React.FC<CheckedRowProps> = ({ isChecked, isDisabled }) => (
  <div className="w-5 h-5">
    {isChecked ? (
      <CheckCircleIcon
        aria-label="checked progress item"
        className={cn('w-5 h-5', {
          'text-green-500': !isDisabled,
          'text-gray-500': isDisabled,
        })}
      />
    ) : (
      <CircleIcon aria-label="unchecked progress item" className="w-5 h-5 text-gray-400" />
    )}
  </div>
);

export type QualityDetailsListTextProps = {
  text: string;
  isComplete?: boolean;
  isDisabled?: boolean;
};

export const QualityDetailsListText: React.FC<QualityDetailsListTextProps> = ({ text, isComplete, isDisabled }) => {
  return (
    <div
      className={cn('flex py-2 justify-between items-start gap-2', { 'opacity-50': isDisabled })}
      role="checkbox"
      aria-checked={isComplete}
      aria-disabled={isDisabled}
      aria-readonly
    >
      <div
        className={cn('text-sm leading-5 font-medium text-gray-700', {
          'opacity-50': isComplete && !isDisabled,
        })}
      >
        {text}
      </div>
      {isComplete !== undefined && <CheckedRow isChecked={isComplete} isDisabled={isDisabled} />}
    </div>
  );
};
