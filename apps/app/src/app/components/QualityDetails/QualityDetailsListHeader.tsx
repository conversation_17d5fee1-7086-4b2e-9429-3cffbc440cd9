import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type QualityDetailsListHeaderProps = {
  title: string;
  progressPercentagesDescription?: string; // Example: "10/20%"
  isComplete?: boolean;
  isDisabled?: boolean;
} & React.ComponentProps<'div'>;

export const QualityDetailsListHeader: React.FC<QualityDetailsListHeaderProps> = ({
  title,
  progressPercentagesDescription,
  isComplete,
  isDisabled,
  ...props
}) => {
  return (
    <div className="flex pt-3 pb-1 justify-between items-center">
      <h3 className="text-xs leading-4 font-semibold tracking-wider uppercase text-gray-400" {...props}>
        {title}
      </h3>
      {progressPercentagesDescription && (
        <div
          className={cn('text-xs leading-4 font-medium', {
            'opacity-50': isDisabled,
            'text-gray-400': !isComplete,
            'text-green-500': isComplete && !isDisabled,
            'text-gray-700': isComplete && isDisabled,
          })}
        >
          {progressPercentagesDescription}
        </div>
      )}
    </div>
  );
};
