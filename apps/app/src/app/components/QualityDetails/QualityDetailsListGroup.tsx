import React, { useId } from 'react';
import { QualityDetailsListHeader } from './QualityDetailsListHeader';

type QualityDetailsListGroup = React.PropsWithChildren<React.ComponentProps<typeof QualityDetailsListHeader>>;

export const QualityDetailsListGroup: React.FC<QualityDetailsListGroup> = ({ children, ...props }) => {
  const groupId = useId();
  const id = `qualityDetailsListGroup-${groupId}`;
  return (
    <div role="group" aria-labelledby={id}>
      <QualityDetailsListHeader id={id} {...props} />
      {children}
    </div>
  );
};
