import React from 'react';
import { fakeObserver, render, screen } from 'tests/test-utils';
import { Name } from './Name';

describe('Name', () => {
  beforeEach(() => {
    window.IntersectionObserver = fakeObserver();
  });

  function setupTest() {
    return render(<Name />);
  }

  it('renders correctly', () => {
    setupTest();

    expect(screen.getByLabelText('myAccount.details.updateName.fields.firstName')).toBeInTheDocument();
    expect(screen.getByLabelText('myAccount.details.updateName.fields.lastName')).toBeInTheDocument();
    expect(screen.getByText('actions.cancel')).toBeInTheDocument();
    expect(screen.getByText('actions.save')).toBeInTheDocument();
  });
});
