import React, { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import InputText from '@shape-construction/arch-ui/src/InputText';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useCurrentUser, useUpdateUser } from 'app/queries/users/users';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as Yup from 'yup';

type FormValues = {
  firstName: string;
  lastName: string;
};

const updateNameFormSchema: Yup.SchemaOf<FormValues> = Yup.object().shape({
  firstName: Yup.string().required('Required field'),
  lastName: Yup.string().required('Required field'),
});

export const Name = () => {
  const navigate = useNavigate();
  const { mutate: updateUser } = useUpdateUser();
  const user = useCurrentUser();
  const messages = useMessageGetter('myAccount.details.updateName');
  const actionMessages = useMessageGetter('actions');
  const [openModal, setOpenModal] = useState(true);

  const form = useForm<FormValues>({
    defaultValues: {
      firstName: user.firstName || '',
      lastName: user.lastName || '',
    },
    resolver: yupResolver(updateNameFormSchema),
  });
  const {
    register,
    handleSubmit,
    formState: { isSubmitting, errors },
  } = form;

  const closeModal = () => {
    setOpenModal(false);
    navigate(-1);
  };

  const submitNameChange = (values: FormValues) => {
    updateUser(
      {
        data: {
          user: {
            first_name: values.firstName,
            last_name: values.lastName,
          },
        },
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('action.success') });
          closeModal();
        },
        onError: () => showErrorToast({ message: messages('action.fail') }),
      }
    );
  };

  return (
    <Modal.Root open={openModal} onClose={closeModal}>
      <Modal.Header onClose={closeModal}>
        <Modal.Title>{messages('title')}</Modal.Title>
        <Modal.SubTitle>{messages('subTitle')}</Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="py-6">
        <form className="space-y-4" onSubmit={handleSubmit(submitNameChange)}>
          <InputText
            {...register('firstName')}
            fullWidth
            label={messages('fields.firstName')}
            error={errors?.firstName?.message}
          />
          <InputText
            {...register('lastName')}
            fullWidth
            label={messages('fields.lastName')}
            error={errors?.lastName?.message}
          />
          {/* This is to make sure inputs trigger form submit event when [Enter] key is pressed */}
          <button type="submit" className="hidden" aria-label="submit" />
        </form>
      </Modal.Content>
      <Modal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={closeModal}>
          {actionMessages('cancel')}
        </Button>
        <Button
          color="primary"
          variant="contained"
          size="md"
          type="submit"
          disabled={isSubmitting}
          onClick={handleSubmit(submitNameChange)}
        >
          {actionMessages('save')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};

export { Name as Component };
