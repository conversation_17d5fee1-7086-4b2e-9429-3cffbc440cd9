import { userFactory } from '@shape-construction/api/factories/users';
import { postApiUsersConfirmationMockHandler } from '@shape-construction/api/handlers-factories/users';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { Email } from './Email';

describe('Email', () => {
  it('renders correctly', () => {
    render(<Email />);

    expect(screen.getByLabelText('myAccount.details.updateEmail.fields.email')).toBeInTheDocument();
    expect(screen.getByText('actions.cancel')).toBeInTheDocument();
    expect(screen.getByText('actions.save')).toBeInTheDocument();
  });

  describe('when user has unconfirmed email', () => {
    it('renders otp confirmation form', () => {
      render(<Email />, { user: userFactory({ unconfirmedEmail: '<EMAIL>' }) });

      expect(screen.getByPlaceholderText('otpConfirmationForm.inputPlaceholder')).toBeInTheDocument();
    });

    it('disables email update input', () => {
      render(<Email />, { user: userFactory({ unconfirmedEmail: '<EMAIL>' }) });

      expect(screen.getByLabelText('myAccount.details.updateEmail.fields.email')).toBeDisabled();
    });

    it('does not render footer', () => {
      render(<Email />, { user: userFactory({ unconfirmedEmail: '<EMAIL>' }) });

      expect(screen.queryByText('actions.cancel')).not.toBeInTheDocument();
      expect(screen.queryByText('actions.save')).not.toBeInTheDocument();
    });

    it('navigates to previous route on successful otp confirmation', async () => {
      server.use(postApiUsersConfirmationMockHandler());
      const history = createMemoryHistory({
        initialEntries: ['/my-profile', '/my-profile/email'],
        initialIndex: 1,
      });
      const route = { path: '/my-profile/email' };
      render(<Email />, {
        user: userFactory({ unconfirmedEmail: '<EMAIL>' }),
        history,
        route,
      });
      await userEvent.type(await screen.findByPlaceholderText('otpConfirmationForm.inputPlaceholder'), '123456');

      const submitButton = screen.getByRole('button', { name: 'otpConfirmationForm.submit' });
      await waitFor(() => expect(submitButton).toBeEnabled());
      userEvent.click(submitButton);

      await waitFor(() => expect(history.location.pathname).toEqual('/my-profile'));
    });
  });
});
