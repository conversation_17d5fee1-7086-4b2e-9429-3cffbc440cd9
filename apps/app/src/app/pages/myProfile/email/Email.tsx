import React, { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import InputEmail from '@shape-construction/arch-ui/src/InputEmail';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { OtpConfirmationForm } from 'app/components/OtpConfirmationForm/OtpConfirmationForm';
import { useCurrentUser, useUpdateUser } from 'app/queries/users/users';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as Yup from 'yup';
import { UnconfirmedAlert } from './components/UnconfirmedAlert';

type FormValues = {
  email: string;
};

const updateEmailFormSchema: Yup.SchemaOf<FormValues> = Yup.object().shape({
  email: Yup.string().email('Must be a valid email').required('Required field'),
});

export const Email = () => {
  const navigate = useNavigate();
  const { mutate: updateUser } = useUpdateUser();
  const user = useCurrentUser();
  const messages = useMessageGetter('myAccount.details.updateEmail');
  const actionMessages = useMessageGetter('actions');
  const [openModal, setOpenModal] = useState(true);
  const askForOtp = !!user.unconfirmedEmail;

  const form = useForm<FormValues>({
    defaultValues: {
      email: user.email || '',
    },
    resolver: yupResolver(updateEmailFormSchema),
  });
  const {
    register,
    handleSubmit,
    formState: { isSubmitting, errors },
  } = form;

  const closeModal = () => {
    setOpenModal(false);
    navigate(-1);
  };

  const submitEmailChange = (values: FormValues) => {
    updateUser(
      {
        data: {
          user: {
            email: values.email,
          },
        },
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('action.success') });
        },
        onError: () => showErrorToast({ message: messages('action.fail') }),
      }
    );
  };

  return (
    <Modal.Root open={openModal} onClose={closeModal}>
      <Modal.Header onClose={closeModal}>
        <Modal.Title>{messages('title')}</Modal.Title>
        <Modal.SubTitle>{messages('subTitle')}</Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="py-6">
        <form className="space-y-4" onSubmit={handleSubmit(submitEmailChange)}>
          <InputEmail
            {...register('email')}
            fullWidth
            label={messages('fields.email')}
            error={errors?.email?.message}
            disabled={askForOtp}
          />
          <UnconfirmedAlert closeOverlay={closeModal} />
          {/* This is to make sure inputs trigger form submit event when [Enter] key is pressed */}
          <button type="submit" className="hidden" aria-label="submit" />
        </form>

        {askForOtp && (
          <div className="mt-8">
            <OtpConfirmationForm userId={user.id} onSuccess={closeModal} />
          </div>
        )}
      </Modal.Content>
      {!askForOtp && (
        <Modal.Footer>
          <Button color="secondary" variant="outlined" size="md" onClick={closeModal}>
            {actionMessages('cancel')}
          </Button>
          <Button
            color="primary"
            variant="contained"
            size="md"
            type="submit"
            disabled={isSubmitting}
            onClick={handleSubmit(submitEmailChange)}
          >
            {actionMessages('save')}
          </Button>
        </Modal.Footer>
      )}
    </Modal.Root>
  );
};

export { Email as Component };
