import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Button from '@shape-construction/arch-ui/src/Button';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { useCurrentUser, useResendConfirmationEmail, useUpdateUser } from 'app/queries/users/users';

export const UnconfirmedAlert = ({ closeOverlay }: any) => {
  const user = useCurrentUser();
  const messages = useMessageGetter('myAccount.details.updateEmail.alert');
  const { mutate: resendConfirmationEmail } = useResendConfirmationEmail();
  const { mutate: updateUser } = useUpdateUser();

  const resetUnconfirmedEmail = () => {
    updateUser(
      {
        data: {
          user: {
            reset_unconfirmed_email: true,
          },
        },
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('cancel.action.success') });
          closeOverlay();
        },
        onError: () => showErrorToast({ message: messages('cancel.action.fail') }),
      }
    );
  };

  if (!user.unconfirmedEmail) {
    return null;
  }

  return (
    <Alert color="primary" justifyContent="start">
      <Alert.Message className="flex flex-col">
        <div>
          <h3 className="text-indigo-800 font-medium">{messages('title')}</h3>
          <p className="mt-2">
            {safeHTMLToReact(messages('description', { EMAIL: user.unconfirmedEmail }), {
              sanitizerOptions: { ALLOWED_TAGS: ['b'] },
            })}
          </p>
        </div>
        <div className="flex mt-4">
          <Button
            color="primary"
            variant="text"
            size="sm"
            onClick={() => {
              resendConfirmationEmail({ data: { email: user.unconfirmedEmail! } });
              closeOverlay();
            }}
          >
            {messages('resend.CTA')}
          </Button>
          <Button
            color="primary"
            variant="text"
            size="sm"
            onClick={() => {
              resetUnconfirmedEmail();
            }}
          >
            {messages('cancel.CTA')}
          </Button>
        </div>
      </Alert.Message>
    </Alert>
  );
};
