import React from 'react';
import { userFactory } from '@shape-construction/api/factories/users';
import {
  patchApiUsersMeMockHandler,
  postApiUsersConfirmationInstructionsMockHandler,
} from '@shape-construction/api/handlers-factories/users';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { UnconfirmedAlert } from './UnconfirmedAlert';

const user = userFactory({
  unconfirmedEmail: '<EMAIL>',
});
const mockProps = {
  closeOverlay: jest.fn(),
};

describe('UnconfirmedAlert', () => {
  beforeEach(() => {
    mockProps.closeOverlay.mockClear();

    server.use(
      postApiUsersConfirmationInstructionsMockHandler(undefined, { status: 204 }),
      patchApiUsersMeMockHandler(() => userFactory())
    );
  });

  it('should resend email and closeOverlay', async () => {
    render(<UnconfirmedAlert {...mockProps} />, { user });

    const button = screen.getByText('myAccount.details.updateEmail.alert.resend.CTA');
    await userEvent.click(button);

    await waitFor(() => expect(mockProps.closeOverlay).toHaveBeenCalledTimes(1));
  });

  it('should reset unconfirmed email and closeOverlay', async () => {
    render(<UnconfirmedAlert {...mockProps} />, { user });

    const button = screen.getByText('myAccount.details.updateEmail.alert.cancel.CTA');
    await userEvent.click(button);

    await waitFor(() => expect(mockProps.closeOverlay).toHaveBeenCalledTimes(1));
  });
});
