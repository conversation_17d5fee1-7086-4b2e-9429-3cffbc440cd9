import React from 'react';
import { activeStorageFileFactory } from '@shape-construction/api/factories/activestorage';
import { userFactory } from '@shape-construction/api/factories/users';
import { postApiDirectUploadsDocumentMockHandler } from '@shape-construction/api/handlers-factories/direct-upload';
import { patchApiUsersMeMockHandler } from '@shape-construction/api/handlers-factories/users';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { UploadAvatar } from './UploadAvatar';

describe('UploadAvatar', () => {
  it('renders the user avatar component', async () => {
    const user = userFactory({ firstName: 'Lauren', lastName: 'Mill' });
    render(<UploadAvatar user={user} />);

    expect(screen.getByRole('button', { name: 'LM myAccount.avatar.change' })).toBeInTheDocument();
  });

  describe('when clicking the avatar image', () => {
    it('uploads the file and opens the modal', async () => {
      const user = userFactory({ firstName: 'Lauren', lastName: 'Mill' });
      render(<UploadAvatar user={user} />);

      const label = screen.getByText('myAccount.avatar.change') as HTMLLabelElement;
      const input = document.getElementById(label.htmlFor) as HTMLInputElement;
      const file = new File(['content'], 'profile.png', { type: 'image/png' });
      await userEvent.upload(input, file);

      await waitFor(async () => {
        expect(
          await screen.findByRole('heading', {
            name: 'myAccount.avatar.modalTitle',
            level: 2,
          })
        ).toBeInTheDocument();
      });
      expect(screen.getByRole('img', { name: 'John Doe' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'myAccount.avatar.cancelCTA' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'myAccount.avatar.saveCTA' })).toBeInTheDocument();
    });

    it('cancels the action and closes the modal', async () => {
      const user = userFactory({ firstName: 'Lauren', lastName: 'Mill' });
      render(<UploadAvatar user={user} />);

      const label = screen.getByText('myAccount.avatar.change') as HTMLLabelElement;
      const input = document.getElementById(label.htmlFor) as HTMLInputElement;
      const file = new File(['content'], 'profile.png', { type: 'image/png' });
      await userEvent.upload(input, file);

      await waitFor(async () => {
        expect(
          await screen.findByRole('heading', {
            name: 'myAccount.avatar.modalTitle',
            level: 2,
          })
        ).toBeInTheDocument();
      });

      await userEvent.click(await screen.findByRole('button', { name: 'myAccount.avatar.cancelCTA' }));

      await waitFor(() => {
        expect(
          screen.queryByRole('heading', {
            name: 'myAccount.avatar.modalTitle',
            level: 2,
          })
        ).not.toBeInTheDocument();
      });
    });

    it('changes the image and closes the modal', async () => {
      let postRequestReceived = false;
      let patchRequestReceived = false;
      server.use(
        postApiDirectUploadsDocumentMockHandler(() => {
          const activeStorageFile = activeStorageFileFactory({
            directUpload: {
              headers: {},
              url: 'http://intercept.me.shape/image/',
            },
          });
          postRequestReceived = true;
          return activeStorageFile;
        }),
        patchApiUsersMeMockHandler(() => {
          patchRequestReceived = true;
          return userFactory();
        })
      );

      const user = userFactory({ firstName: 'Lauren', lastName: 'Mill' });
      render(<UploadAvatar user={user} />);

      const label = screen.getByText('myAccount.avatar.change') as HTMLLabelElement;
      const file = new File(['content'], 'profile.png', { type: 'image/png' });
      await userEvent.upload(label, file);

      await waitFor(async () => {
        expect(
          await screen.findByRole('heading', {
            name: 'myAccount.avatar.modalTitle',
            level: 2,
          })
        ).toBeInTheDocument();
      });

      await userEvent.click(await screen.findByRole('button', { name: 'myAccount.avatar.saveCTA' }));

      await waitFor(async () => {
        expect(postRequestReceived).toBe(true);
      });
      await waitFor(async () => {
        expect(patchRequestReceived).toBe(true);
      });
      await waitFor(() => {
        expect(
          screen.queryByRole('heading', {
            name: 'myAccount.avatar.modalTitle',
            level: 2,
          })
        ).not.toBeInTheDocument();
      });
    });
  });
});
