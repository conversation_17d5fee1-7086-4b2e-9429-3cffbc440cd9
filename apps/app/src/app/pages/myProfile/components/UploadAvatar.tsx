import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { UserSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import Button from '@shape-construction/arch-ui/src/Button';
import * as FileUpload from '@shape-construction/arch-ui/src/FileUpload';
import FullScreenModal from '@shape-construction/arch-ui/src/FullScreenModal';
import { showErrorToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { useMutation } from '@tanstack/react-query';
import { MAX_IMAGE_SIZE_IN_BYTES, SUPPORTED_IMAGE_MIME_TYPES } from 'app/constants/FileUpload';
import { useFileUploadValidator } from 'app/hooks/useFileUploadValidator';
import { DirectUpload } from 'app/lib/direct-upload/direct-upload';
import { useUpdateUser } from 'app/queries/users/users';

export interface UploadAvatarProps {
  user: UserSchema;
}

type Image = {
  file: File;
  preview: string;
};

export const UploadAvatar: React.FC<UploadAvatarProps> = ({ user }) => {
  const isDesktop = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('myAccount.avatar');
  const errors = useMessageGetter('errors.fileUpload');
  const uploadErrorMessage = useMessageGetter('error');
  const [newImage, setNewImage] = useState<Image | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const { mutate: uploadAccountAvatar } = useMutation({
    mutationFn: ({ file }: { file: File }) => DirectUpload(file, 'image'),
    onError: (error) => {
      showErrorToast({ message: error.message });
    },
  });
  const { mutate: updateUser } = useUpdateUser();
  const { validateFile, handleValidationErrors } = useFileUploadValidator({
    maxSizeInBytes: MAX_IMAGE_SIZE_IN_BYTES,
    allowedFileTypes: SUPPORTED_IMAGE_MIME_TYPES,
    errorMessages: {
      fileSizeMin: (filename, min) => errors('fileSizeMin', { filename, min }),
      fileSizeMax: (filename, max) => errors('fileSizeMax', { filename, max }),
      fileTypeInvalid: (filename) => errors('fileTypeInvalid', { filename }),
    },
  });

  const handleImageChange = (files: File[]) => {
    const file = files[0];
    if (!file) return;
    const result = validateFile(file);
    handleValidationErrors(result);

    if (result.isValid) {
      setNewImage({
        file,
        preview: URL.createObjectURL(file),
      });
      setModalOpen(true);
    }
  };

  const handleUploadAvatar = async () => {
    const file = newImage?.file;

    if (file) {
      uploadAccountAvatar(
        { file },
        {
          onSuccess: (signedId) => {
            if (!signedId) {
              throw new Error(uploadErrorMessage('uploadError'));
            }
            updateUser(
              { data: { user: { avatar: signedId } } },
              {
                onSettled: () => {
                  setNewImage(null);
                  closeModal();
                },
              }
            );
          },
          onError: (error) => {
            showErrorToast({ message: error.message });
          },
        }
      );
    }
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const imagePreview: string = newImage?.preview || '';

  return (
    <>
      <FileUpload.Root id="avatar-input" onChange={handleImageChange}>
        <FileUpload.Trigger>
          <div className="relative cursor-pointer">
            <UserAvatar user={user} size={isDesktop ? '3xl' : 'xl'} />
            <FileUpload.Label className="absolute inset-0 flex h-full w-full items-center justify-center rounded-full bg-black/60 text-xs font-medium text-white opacity-0 transition-opacity hover:opacity-100 active:opacity-100">
              {messages('change')}
            </FileUpload.Label>
          </div>
        </FileUpload.Trigger>
      </FileUpload.Root>

      {modalOpen && (
        <FullScreenModal.Root open onClose={closeModal}>
          <FullScreenModal.Header onClose={closeModal}>
            <FullScreenModal.Title>{messages('modalTitle')}</FullScreenModal.Title>
          </FullScreenModal.Header>
          <FullScreenModal.Content>
            <img src={imagePreview} alt={user.name} className="w-full h-full object-contain" />
          </FullScreenModal.Content>
          <FullScreenModal.Footer>
            <Button type="button" color="secondary" variant="outlined" size="md" onClick={closeModal}>
              {messages('cancelCTA')}
            </Button>
            <Button type="button" color="primary" variant="contained" size="md" onClick={handleUploadAvatar}>
              {messages('saveCTA')}
            </Button>
          </FullScreenModal.Footer>
        </FullScreenModal.Root>
      )}
    </>
  );
};
