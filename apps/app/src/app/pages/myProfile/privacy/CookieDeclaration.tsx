import { useEffect, useRef, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { environment } from 'app/config/environment';
import { useLocation, useNavigate } from 'react-router';

export const CookieDeclaration = () => {
  const messages = useMessageGetter('myAccount.privacy');
  const [openModal, setOpenModal] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  const closeModal = () => {
    setOpenModal(false);
    if (location.key !== 'default') {
      navigate(-1);
    } else {
      navigate('/my-profile');
    }
  };

  const cookieDeclarationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const script = document.createElement('script');

    script.id = 'CookieDeclaration';
    script.src = `https://consent.cookiebot.com/${environment.COOKIEBOT_DOMAIN_GROUP_ID}/cd.js`;
    script.type = 'text/javascript';
    script.async = true;

    cookieDeclarationRef.current?.appendChild(script);

    return () => {
      cookieDeclarationRef.current?.removeChild(script);
    };
  }, []);

  return (
    <Modal.Root open={openModal} onClose={closeModal}>
      <Modal.Header onClose={closeModal}>
        <Modal.Title>{messages('cookieDeclaration')}</Modal.Title>
      </Modal.Header>
      <Modal.Content className="py-6">
        <div ref={cookieDeclarationRef} />
      </Modal.Content>
    </Modal.Root>
  );
};

export { CookieDeclaration as Component };
