import React, { useEffect } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import { ChevronRightIcon, DocumentTextIcon, ShieldCheckIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Page from '@shape-construction/arch-ui/src/Page';
import StackedList from '@shape-construction/arch-ui/src/StackedList';
import StackedListHeader from '@shape-construction/arch-ui/src/StackedListHeader';
import StackedListItem from '@shape-construction/arch-ui/src/StackedListItem';
import StackedListItemTitle from '@shape-construction/arch-ui/src/StackedListItemTitle';
import { type DateType, parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { useCurrentUser } from 'app/queries/users/users';
import { Link, Outlet } from 'react-router';
import { UploadAvatar } from './components/UploadAvatar';

const UnconfirmedEmailNotice = ({ email }: { email: string | null }) => {
  const unconfirmedEmailMessage = useMessage('myAccount.details.unconfirmedEmail', {
    EMAIL: email!,
  });

  if (!email) return null;

  return (
    <div className="mt-1 flex gap-1 text-blue-700">
      <div className="shrink-0">
        <InformationCircleIcon className="h-4 w-4 text-blue-400" />
      </div>
      <p className="text-xs">
        {safeHTMLToReact(unconfirmedEmailMessage, {
          sanitizerOptions: { ALLOWED_TAGS: ['b'] },
        })}
      </p>
    </div>
  );
};

export const MyProfile = () => {
  const messages = useMessageGetter('myAccount');
  const { setLayoutConfig } = useLayoutContext();
  const user = useCurrentUser();

  const listItems = [
    {
      title: useMessage('myAccount.details.firstLastName'),
      route: '/my-profile/name',
      value: user.name,
    },
    {
      title: useMessage('myAccount.details.email'),
      route: '/my-profile/email',
      value: user.email,
      children: <UnconfirmedEmailNotice email={user.unconfirmedEmail} />,
    },
    {
      title: useMessage('myAccount.details.password'),
      route: '/my-profile/password',
      value: '**********',
    },
  ];

  const onCookiePreferencesClick = () => {
    window.Cookiebot?.renew?.();
  };

  useEffect(() => {
    setLayoutConfig({
      ...LayoutConfigs.titleWithBackButtonVariant,
      ...LayoutConfigs.hideSideDrawer,
    });
  }, [setLayoutConfig]);

  return (
    <Page data-cy="my-account">
      <Page.Header title={messages('title')} />
      <Page.Body>
        <div className="flex items-center gap-5">
          <UploadAvatar user={user} />
          <div>
            <h3 className="text-xl font-semibold md:text-2xl">{user.name}</h3>
            <p className="text-sm font-medium text-gray-500">
              {messages('avatar.memberSince', {
                SIGNUP_DATE: parseDateWithFormat(user.signupDate as DateType, 'MMMM YYYY'),
              })}
            </p>
          </div>
        </div>
        <div className="mt-8" />
        <StackedList>
          <StackedListHeader title={messages('details.title')} />
          {listItems.map(({ title, value, route, children }) => (
            <StackedListItem key={route} asChild>
              <Link
                to={route}
                className="flex w-full h-16 items-center justify-between px-4 py-4 sm:px-6 hover:bg-neutral-alpha-subltest-hovered cursor-pointer"
              >
                <div className="flex flex-1 flex-col">
                  <StackedListItemTitle title={title} description={value!} />
                  {children}
                </div>
                <ChevronRightIcon className="w-5 h-5 text-icon-neutral-subtle" />
              </Link>
            </StackedListItem>
          ))}
        </StackedList>
        <div className="mt-8" />
        <StackedList>
          <StackedListHeader title={messages('privacy.title')} />
          <StackedListItem asChild>
            <button
              type="button"
              className="flex w-full h-16 items-center justify-between px-4 py-4 sm:px-6 hover:bg-neutral-alpha-subltest-hovered cursor-pointer"
              onClick={onCookiePreferencesClick}
            >
              <div className="flex flex-1 items-center gap-2">
                <ShieldCheckIcon className="text-icon-neutral-subtle" />
                <StackedListItemTitle title={messages('privacy.cookiePreferences')} description={''} />
              </div>
              <ChevronRightIcon className="w-5 h-5 text-icon-neutral-subtle cursor-pointer" />
            </button>
          </StackedListItem>
          <StackedListItem asChild>
            <Link
              to="/my-profile/cookie-declaration"
              className="flex w-full h-16 items-center justify-between px-4 py-4 sm:px-6 hover:bg-neutral-alpha-subltest-hovered cursor-pointer"
            >
              <div className="flex flex-1 items-center gap-2">
                <DocumentTextIcon className="text-icon-neutral-subtle" />
                <StackedListItemTitle title={messages('privacy.cookieDeclaration')} description={''} />
              </div>
              <ChevronRightIcon className="w-5 h-5 text-icon-neutral-subtle" />
            </Link>
          </StackedListItem>
        </StackedList>
        <Outlet />
      </Page.Body>
    </Page>
  );
};

export { MyProfile as Component };
