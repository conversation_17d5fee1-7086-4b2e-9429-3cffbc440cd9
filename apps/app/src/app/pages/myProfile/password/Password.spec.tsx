import React from 'react';
import { fakeObserver, render, screen } from 'tests/test-utils';
import { Password } from './Password';

describe('Password', () => {
  beforeEach(() => {
    window.IntersectionObserver = fakeObserver();
  });

  function setupTest() {
    return render(<Password />);
  }

  it('renders correctly', () => {
    setupTest();

    expect(screen.getByLabelText('myAccount.details.updatePassword.fields.currentPassword')).toBeInTheDocument();
    expect(screen.getByLabelText('myAccount.details.updatePassword.fields.newPassword')).toBeInTheDocument();
    expect(screen.getByLabelText('myAccount.details.updatePassword.fields.repeatNewPassword')).toBeInTheDocument();
    expect(screen.getByText('actions.cancel')).toBeInTheDocument();
    expect(screen.getByText('actions.save')).toBeInTheDocument();
  });

  describe('Checklist visibility', () => {
    describe('when the password is focused', () => {
      it('becomes visible', async () => {
        const { user } = render(<Password />);

        const passwordField = await screen.findByLabelText('myAccount.details.updatePassword.fields.newPassword');
        await user.click(passwordField);

        expect(screen.getByText('auth.passwordCheckList.min').closest('div[aria-hidden]')).toHaveAttribute(
          'aria-hidden',
          'false'
        );
      });
    });

    describe('when the password is not focused', () => {
      it('remains visible if the password checklist has errors', async () => {
        const { user } = render(<Password />);

        const passwordField = await screen.findByLabelText('myAccount.details.updatePassword.fields.newPassword');
        await user.click(passwordField);
        await user.type(passwordField, 'short');
        await user.click(screen.getByText('actions.save'));
        //Shift focus
        await user.click(screen.getByText('myAccount.details.updatePassword.fields.repeatNewPassword'));

        expect(screen.getByText('auth.passwordCheckList.min').closest('div[aria-hidden]')).toHaveAttribute(
          'aria-hidden',
          'false'
        );
      });

      it('becomes hidden if checklist has no errors ', async () => {
        const { user } = render(<Password />);

        await user.type(
          await screen.findByLabelText('myAccount.details.updatePassword.fields.newPassword'),
          'Password@234!'
        );
        await user.click(screen.getByText('myAccount.details.updatePassword.fields.repeatNewPassword'));

        expect(screen.getByText('auth.passwordCheckList.min').closest('div[aria-hidden]')).toHaveAttribute(
          'aria-hidden',
          'true'
        );
      });
    });
  });
});
