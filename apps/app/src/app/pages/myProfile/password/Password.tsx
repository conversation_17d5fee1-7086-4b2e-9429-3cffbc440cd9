import React, { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import CheckList from '@shape-construction/arch-ui/src/CheckList';
import InputPassword from '@shape-construction/arch-ui/src/InputPassword';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useCheckListValidation } from 'app/hooks/useCheckListValidation';
import { useCurrentUser, useUpdateUser } from 'app/queries/users/users';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as Yup from 'yup';

type FormValues = {
  currentPassword: string;
  newPassword: string;
  repeatNewPassword: string;
};

const updatePasswordFormSchema: Yup.SchemaOf<FormValues> = Yup.object({
  currentPassword: Yup.string().min(8, 'Passwords must be at least 8 characters').required('Required field'),
  newPassword: Yup.string()
    .required('Required field')
    .min(8, 'Passwords must be at least 8 characters')
    .matches(/[A-Z]/, { name: 'no_uppercase' })
    .matches(/[a-z]/, { name: 'no_lowercase' })
    .matches(/\d/, { name: 'no_number' })
    .matches(/[^A-Za-z0-9]/, { name: 'no_special_char' }),
  repeatNewPassword: Yup.string()
    .required('Required field')
    .test('passwords_match', 'Passwords must match', function (value) {
      return this.parent.newPassword === value && value !== '';
    }),
});

const newPasswordKeys = ['min', 'no_uppercase', 'no_lowercase', 'no_number', 'no_special_char'];

export const Password = () => {
  const navigate = useNavigate();
  const user = useCurrentUser();
  const { mutate: updateUser } = useUpdateUser();
  const messages = useMessageGetter('myAccount.details.updatePassword');
  const actionMessages = useMessageGetter('actions');
  const [openModal, setOpenModal] = useState(true);
  const [newPasswordHasFocus, setNewPasswordHasFocus] = useState<boolean>(false);

  const form = useForm<FormValues>({
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      repeatNewPassword: '',
    },
    resolver: yupResolver(updatePasswordFormSchema, { abortEarly: false }),
    criteriaMode: 'all',
    mode: 'onSubmit',
  });
  const {
    register,
    handleSubmit,
    formState: { isSubmitting, errors },
  } = form;

  const validations = useCheckListValidation({
    validationKeys: newPasswordKeys,
    errors: errors.newPassword,
    isSubmitting: isSubmitting,
  });

  const closeModal = () => {
    setOpenModal(false);
    navigate(-1);
  };

  const submitPasswordChange = (values: FormValues) => {
    updateUser(
      {
        data: {
          user: {
            email: user.email,
            password: values.newPassword,
            current_password: values.currentPassword,
          },
        },
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('action.success') });
          closeModal();
        },
        onError: () => showErrorToast({ message: messages('action.fail') }),
      }
    );
  };

  const hasNewPasswordError = Object.values(validations).some((v) => v === 'error');
  const showChecklist = newPasswordHasFocus || hasNewPasswordError;
  const passwordCheckListMessages = useMessageGetter('auth.passwordCheckList');

  return (
    <Modal.Root open={openModal} onClose={closeModal}>
      <Modal.Header onClose={closeModal}>
        <Modal.Title>{messages('title')}</Modal.Title>
        <Modal.SubTitle>{messages('subTitle')}</Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="py-6">
        <form className="space-y-4" onSubmit={handleSubmit(submitPasswordChange)}>
          <InputPassword
            {...register('currentPassword')}
            fullWidth
            label={messages('fields.currentPassword')}
            error={errors?.currentPassword?.message}
          />
          <div>
            <InputPassword
              fullWidth
              {...register('newPassword', {
                onChange: () => form.trigger('newPassword'),
                onBlur: () => setNewPasswordHasFocus(false),
              })}
              label={messages('fields.newPassword')}
              error={hasNewPasswordError}
              onFocus={() => setNewPasswordHasFocus(true)}
            />
            <CheckList.Root isVisible={showChecklist} className="mt-3 flex flex-col gap-4">
              <CheckList.Item variant={validations.min}>{passwordCheckListMessages('min')}</CheckList.Item>
              <CheckList.Item variant={validations.no_uppercase}>
                {passwordCheckListMessages('uppercase')}
              </CheckList.Item>
              <CheckList.Item variant={validations.no_lowercase}>
                {passwordCheckListMessages('lowercase')}
              </CheckList.Item>
              <CheckList.Item variant={validations.no_number}>{passwordCheckListMessages('number')}</CheckList.Item>
              <CheckList.Item variant={validations.no_special_char}>
                {passwordCheckListMessages('special')}
              </CheckList.Item>
            </CheckList.Root>
          </div>
          <InputPassword
            fullWidth
            {...register('repeatNewPassword')}
            label={messages('fields.repeatNewPassword')}
            error={errors.repeatNewPassword?.message}
          />
          {/* This is to make sure inputs trigger form submit event when [Enter] key is pressed */}
          <button type="submit" className="hidden" aria-label="submit" />
        </form>
      </Modal.Content>
      <Modal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={closeModal}>
          {actionMessages('cancel')}
        </Button>
        <Button
          color="primary"
          variant="contained"
          size="md"
          type="submit"
          disabled={isSubmitting}
          onClick={handleSubmit(submitPasswordChange)}
        >
          {actionMessages('save')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};

export { Password as Component };
