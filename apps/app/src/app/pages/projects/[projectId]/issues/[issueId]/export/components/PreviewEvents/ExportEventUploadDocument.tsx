import React, { useMemo } from 'react';
import { useMessage } from '@messageformat/react';
import type {
  DocumentSchema,
  IssueEventParametersUploadDocumentSchema,
  IssueEventSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import FileThumbnail from '@shape-construction/arch-ui/src/FileThumbnail/FileThumbnail';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from 'app/components/Feed/feed-helper';

type ExportEventUploadDocumentProps = {
  date: IssueEventSchema['date'];
  ownerImgUrl: UserBasicDetailsSchema['avatarUrl'];
  ownerName: UserBasicDetailsSchema['name'];
  resources: IssueEventParametersUploadDocumentSchema['resources'];
};

export const ExportEventUploadDocument = ({
  date,
  ownerImgUrl,
  ownerName,
  resources,
}: ExportEventUploadDocumentProps) => {
  const { document } = resources;
  const eventMessage = useMessage('issue.detail.export.events.upload_document', {
    ownerName: boldNameMessage(ownerName),
  });

  const documents: (DocumentSchema | null)[] = useMemo(() => {
    if (!document) return [null];

    return [
      {
        ...document,
        availableActions: {
          edit: false,
          delete: false,
        },
      },
    ];
  }, [document]);

  return (
    <>
      <FeedEventAction
        avatar={
          <div className="flex items-center">
            <Avatar text={ownerName} imgURL={ownerImgUrl} />
          </div>
        }
        date={date}
        title={parseReactElement(eventMessage)}
      >
        <div className="mx-auto md:mx-0">
          {documents.map((field) => {
            if (!field) return null;

            return (
              <FileThumbnail
                as="div"
                key={`document-${field.id}`}
                extension={field.extension}
                caption={field.caption || field.filename}
                fileId={field.id}
                thumbnailUrl={field.imageUrl?.s}
              />
            );
          })}
        </div>
      </FeedEventAction>
    </>
  );
};
