import React from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useMessage, useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Button from '@shape-construction/arch-ui/src/Button';
import { FileGallery } from '@shape-construction/arch-ui/src/FileGallery/FileGallery';
import InputCheckbox from '@shape-construction/arch-ui/src/InputCheckbox';
import InputText from '@shape-construction/arch-ui/src/InputText';
import InputTextArea from '@shape-construction/arch-ui/src/InputTextArea';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { showSuccessToast, showWarningToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useLocalStorage } from '@shape-construction/hooks';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useQuery } from '@tanstack/react-query';
import { imageAcceptTypes } from 'app/components/Gallery/constants';
import { MediaPicker, type MediaPickerOptions } from 'app/components/MediaPicker/MediaPicker';
import UtilsStats from 'app/components/Utils/UtilsStats';
import { environment } from 'app/config/environment';
import { MAX_IMAGE_SIZE_IN_BYTES, SUPPORTED_IMAGE_MIME_TYPES } from 'app/constants/FileUpload';
import { useFileUploadValidator } from 'app/hooks/useFileUploadValidator';
import { buildImageUpload, type IssueImageUpload } from 'app/localData/issueImages';
import { buildIssue } from 'app/localData/issues';
import { useIssueSubmit, useQuickCreateIssue } from 'app/queries/issues/issues';
import { useProjectPerson } from 'app/queries/projects/people';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { useFieldArray, useForm } from 'react-hook-form';
import { useLocation, useNavigate, useParams } from 'react-router';
import * as Yup from 'yup';
import { IssueMediaUpload } from '../components/IssueMediaUpload';

const useValidationSchema = () => {
  const messages = useMessageGetter('issue.quickCapture.fields');

  return Yup.object().shape({
    title: Yup.string().required(messages('title.errors.required')).max(80, messages('title.errors.length')),
    createAnother: Yup.boolean(),
    description: Yup.string(),
    images: Yup.array(),
  });
};

type FormValues = {
  title: string;
  createAnother?: boolean;
  description?: string;
  images?: IssueImageUpload[];
};
const initialState: FormValues = {
  title: '',
  createAnother: false,
  description: undefined,
  images: [],
};
const QUICK_CAPTURE_HINT_STORAGE_KEY = 'quick_capture_hint';

export const QuickCapture: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const channelId = searchParams.get('channelId') ?? '';
  const source = searchParams.get('source') ?? '';
  const isFromChannels = source === 'channels';

  const navigate = useNavigate();

  const onMutateCallback = (online?: boolean) => {
    if (online) return;

    showWarningToast({ message: uploadSyncOfflineMessage, alignContent: 'start' });

    if (getValues().createAnother) return reset();

    navigate(`/projects/${projectId}/issues/lists/unpublished`);
  };

  const onSuccessCallback = async (issue: IssueSchema, online?: boolean) => {
    if (online) return;

    await submitIssueAsync({ projectId: issue.projectId, issueId: issue.id });

    showSuccessToast({
      message: publishIssueSuccess,
      alignContent: 'start',
    });

    navigate(`/projects/${projectId}/issues/lists/my-issues`);
  };

  const { mutate: submitIssue, mutateAsync: submitIssueAsync } = useIssueSubmit();
  const { mutate: quickCreateIssue } = useQuickCreateIssue({ onMutateCallback, onSuccessCallback });

  const { projectId } = useParams() as { projectId: string };
  const [showHint, setShowHint] = useLocalStorage(QUICK_CAPTURE_HINT_STORAGE_KEY, true);
  const messages = useMessageGetter('issue.quickCapture');
  const fileUploadErrors = useMessageGetter('errors.fileUpload');
  const uploadSyncOfflineMessage = useMessage('issue.uploadSync.status.offline');
  const publishIssueSuccess = useMessage('issue.detail.stateActions.draft.publishSuccess.issue');

  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const { data: currentProjectPerson } = useProjectPerson(projectId, project?.currentTeamMemberId!, {
    query: {
      enabled: !!project,
    },
  });

  const validationSchema: Yup.SchemaOf<FormValues> = useValidationSchema();
  const {
    control,
    getValues,
    formState: { errors },
    handleSubmit,
    register,
    reset,
  } = useForm<FormValues>({ defaultValues: initialState, resolver: yupResolver(validationSchema) });
  const { fields: images, append: addImage } = useFieldArray({ control, name: 'images' });
  const imageFiles = images.map((image) => ({
    ...image,
    mediaUrl: {
      large: image.url.xxl,
      medium: image.url.l,
      thumbnail: image.url.s,
    },
    downloadUrl: image.url.download,
    caption: image.filename,
    isUploading: false,
  }));

  const { validateFiles, handleValidationErrors } = useFileUploadValidator({
    maxSizeInBytes: MAX_IMAGE_SIZE_IN_BYTES,
    allowedFileTypes: SUPPORTED_IMAGE_MIME_TYPES,
    errorMessages: {
      fileSizeMin: (filename, min) => fileUploadErrors('fileSizeMin', { filename, min }),
      fileSizeMax: (filename, max) => fileUploadErrors('fileSizeMax', { filename, max }),
      fileTypeInvalid: (filename) => fileUploadErrors('fileTypeInvalid', { filename }),
    },
  });

  const onNewImages = (files: File[]) => {
    const results = validateFiles(files);
    handleValidationErrors(results);

    const validFiles = results.filter((result) => result.isValid).map((result) => result.file);

    validFiles.forEach((file) => {
      addImage(buildImageUpload(file, String(Math.random()), projectId, UtilsStats.uuidv4()));
    });
  };

  const onSubmit = async ({ createAnother, ...values }: FormValues) => {
    const issue = { ...values };

    quickCreateIssue(
      {
        ...buildIssue({ ...issue, draft_assignee_id: currentProjectPerson?.id }, projectId),
        idempotencyKey: UtilsStats.uuidv4(),
      },
      {
        onSettled: (issue: IssueSchema | undefined) => {
          if (!issue) return;

          submitIssue(
            { projectId: issue.projectId, issueId: issue.id },
            {
              onSuccess: () => {
                showSuccessToast({
                  message: publishIssueSuccess,
                  alignContent: 'start',
                });

                if (createAnother) return reset();

                if (isFromChannels) {
                  window.location.replace(`${environment.CHANNELS_URL}/channel/${channelId}`);
                }

                return navigate(`/projects/${projectId}/issues/lists/my-issues`);
              },
            }
          );
        },
      }
    );
  };

  const onClose = () => {
    const redirectTo = location.state?.background ?? `/projects/${projectId}/issues`;
    navigate(redirectTo);
  };

  const mediaPickerOptions: MediaPickerOptions = {
    camera: {
      enabled: true,
      multiple: true,
      onSelectFiles: onNewImages,
    },
    documents: {
      enabled: true,
      accept: imageAcceptTypes,
      multiple: true,
      onSelectFiles: onNewImages,
    },
  };

  return (
    <Modal.Root open onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{messages('title')}</Modal.Title>
      </Modal.Header>
      <Modal.Content className="py-6 space-y-6">
        {showHint && (
          <Alert color="primary" emphasis="subtle" format="inline" onClose={() => setShowHint(false)}>
            <Alert.Message>{parseReactElement(messages('hint'))}</Alert.Message>
          </Alert>
        )}
        <form id="quick-capture" onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <InputText
            label={messages('fields.title.label')}
            placeholder={messages('fields.title.placeholder')}
            {...register('title')}
            error={errors.title?.message}
          />
          <InputTextArea
            label={messages('fields.description.label')}
            placeholder={messages('fields.description.placeholder')}
            {...register('description')}
            error={errors.description?.message}
          />
          <div>
            <div className="mb-1 flex justify-between items-center">
              <span className="block text-sm font-medium text-gray-700">Media</span>
              {imageFiles.length > 0 && <MediaPicker options={mediaPickerOptions} />}
            </div>

            {imageFiles.length === 0 && (
              <IssueMediaUpload title={messages('fields.media.label')} onUploadImages={onNewImages} />
            )}

            {imageFiles.length > 0 && <FileGallery files={imageFiles} onFileSelect={() => {}} layout="row" />}
          </div>
          <button type="submit" className="hidden" aria-label="submit" />
        </form>
      </Modal.Content>
      <Modal.Footer>
        <div className="mr-1">
          <InputCheckbox
            {...register('createAnother')}
            label={messages('fields.createAnother.label')}
            form="quick-capture"
          />
        </div>
        <Button color="secondary" variant="outlined" size="md" onClick={onClose}>
          {messages('actions.cancel')}
        </Button>
        <Button color="primary" variant="contained" size="md" type="submit" onClick={handleSubmit(onSubmit)}>
          {messages('actions.create')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};

export { QuickCapture as Component };
