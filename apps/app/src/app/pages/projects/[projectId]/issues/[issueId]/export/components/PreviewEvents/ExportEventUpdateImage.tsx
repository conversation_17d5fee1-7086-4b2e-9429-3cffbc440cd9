import React from 'react';
import { useMessage } from '@messageformat/react';
import type { IssueEventSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import FeedEventAction from '@shape-construction/arch-ui/src/FeedEventAction';
import { parseReactElement } from '@shape-construction/utils/dom';
import { boldNameMessage } from 'app/components/Feed/feed-helper';

type ExportEventUpdateImageProps = {
  date: IssueEventSchema['date'];
  ownerImgUrl: UserBasicDetailsSchema['avatarUrl'];
  ownerName: UserBasicDetailsSchema['name'];
};

export const ExportEventUpdateImage = ({ date, ownerImgUrl, ownerName }: ExportEventUpdateImageProps) => {
  const eventMessage = useMessage('issue.detail.export.events.update_image', {
    ownerName: boldNameMessage(ownerName),
  });

  return (
    <FeedEventAction
      avatar={
        <div className="flex items-center">
          <Avatar text={ownerName} imgURL={ownerImgUrl} />
        </div>
      }
      date={date}
      title={parseReactElement(eventMessage)}
    />
  );
};
