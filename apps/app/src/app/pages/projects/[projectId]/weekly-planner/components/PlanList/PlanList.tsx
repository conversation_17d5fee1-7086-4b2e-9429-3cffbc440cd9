import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanListSchema } from '@shape-construction/api/src/types';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import { parseReactElement } from '@shape-construction/utils/dom';
import { PlanListItem } from './PlanListItem';

export type PlanListProps = {
  plans: WeeklyWorkPlanListSchema;
  onNext: (cursor: string | null) => void;
  onPrevious: (cursor: string | null) => void;
};

export const PlanList: React.FC<PlanListProps> = ({ plans, onNext, onPrevious }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.table');
  const paginationMessages = useMessageGetter('table.cursorPagination');

  return (
    <Table.Container className="container max-w-7xl overflow-x-auto">
      <Table className="table-fixed">
        <Table.Heading>
          <Table.Row>
            <Table.Header>{messages('heading.name')}</Table.Header>
            <Table.Header>{messages('heading.period')}</Table.Header>
            <Table.Header>{messages('heading.author')}</Table.Header>
            <Table.Header>{messages('heading.lastUpdated')}</Table.Header>
            <Table.Header>{messages('heading.status')}</Table.Header>
            <Table.Header className="w-24" />
          </Table.Row>
        </Table.Heading>

        <Table.Body className="[&_tr]:cursor-pointer [&_tr]:last:border-b-0">
          {plans.entries.map((plan) => (
            <PlanListItem key={plan.id} plan={plan} />
          ))}
        </Table.Body>

        <tfoot>
          <Table.Row>
            <Table.Cell colSpan={100}>
              <Pagination
                {...plans.meta}
                onNext={onNext}
                onPrevious={onPrevious}
                previousButtonLabel={paginationMessages('previous')}
                nextButtonLabel={paginationMessages('next')}
                resultsCountLabel={parseReactElement(
                  paginationMessages('resultsCount', {
                    count: plans.entries.length,
                    total: plans.meta.total,
                  })
                )}
              />
            </Table.Cell>
          </Table.Row>
        </tfoot>
      </Table>
    </Table.Container>
  );
};
