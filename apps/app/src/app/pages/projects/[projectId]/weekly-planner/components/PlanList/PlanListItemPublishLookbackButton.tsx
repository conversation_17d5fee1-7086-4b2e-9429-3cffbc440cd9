import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { CheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { useModal } from '@shape-construction/hooks';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import useExports from 'app/components/Exports/useExports';
import { PublishLookbackConfirmationModal } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/PublishLookbackConfirmationModal/PublishLookbackConfirmationModal';
import { usePublishLookbackWeeklyWorkPlan } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';

export const PlanListItemPublishLookbackButton = ({
  plan,
  projectId,
}: {
  plan: WeeklyWorkPlanSchema;
  projectId: ProjectSchema['id'];
}) => {
  const actionMessages = useMessageGetter('weeklyPlanner.workPlans.table.actions');
  const messages = useMessageGetter('weeklyPlanner.workPlans.publishLookback.modal');
  const modal = useModal(false);

  const { mutate: publishLookbackPlan, isPending: isPublishingLookback } = usePublishLookbackWeeklyWorkPlan();
  const { addToPendingExports } = useExports();

  const onPublishLookback = () => {
    publishLookbackPlan(
      { projectId, weeklyWorkPlanId: plan.id },
      {
        onSuccess: (data) => {
          if (data?.exportQueuedTask) {
            showSuccessToast({
              message: safeHTMLToReact(messages('successToast', { planTitle: plan.title })),
            });
            addToPendingExports(data.exportQueuedTask);
          }
          modal.closeModal();
        },
      }
    );
  };

  return (
    <>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <IconButton
            size="xs"
            variant="outlined"
            shape="square"
            color="secondary"
            icon={CheckCircleIcon}
            aria-label={actionMessages('publishLookback')}
            disabled={isPublishingLookback}
            onClick={modal.openModal}
          />
        </Tooltip.Trigger>
        <Tooltip.Content>{actionMessages('publishLookback')}</Tooltip.Content>
      </Tooltip.Root>

      <PublishLookbackConfirmationModal
        isOpen={modal.open}
        onClose={modal.closeModal}
        onConfirm={onPublishLookback}
        disabled={isPublishingLookback}
      />
    </>
  );
};
