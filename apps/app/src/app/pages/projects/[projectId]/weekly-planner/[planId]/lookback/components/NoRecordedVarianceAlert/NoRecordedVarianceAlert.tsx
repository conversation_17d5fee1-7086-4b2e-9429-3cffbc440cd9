import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanActivityListSchema } from '@shape-construction/api/src/types';
import Alert from '@shape-construction/arch-ui/src/Alert';
import { parseReactElement } from '@shape-construction/utils/dom';
import { isVarianceMissing } from '../../../../utils';

export type NoRecordedVarianceAlertProps = {
  activities: WeeklyWorkPlanActivityListSchema;
};

export const NoRecordedVarianceAlert = ({ activities }: NoRecordedVarianceAlertProps) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.lookback.planStatus.reviewing');

  const missingVarianceCount = useMemo(
    () =>
      activities.entries.reduce((acc, row) => {
        const increment = isVarianceMissing(row) ? 1 : 0;
        return acc + increment;
      }, 0),
    [activities]
  );

  if (missingVarianceCount === 0) return null;

  return (
    <Alert color="primary" rounded={false}>
      <Alert.Message>{parseReactElement(messages('alert', { count: missingVarianceCount }))}</Alert.Message>
    </Alert>
  );
};
