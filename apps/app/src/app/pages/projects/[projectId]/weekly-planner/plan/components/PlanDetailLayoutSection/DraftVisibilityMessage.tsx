import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { parseReactElement } from '@shape-construction/utils/dom';

export const DraftVisibilityMessage: React.FC = () => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.details.draftVisibility');
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open}>
      <Popover.Trigger asChild>
        <Button size="xxs" variant="outlined" color="secondary" onClick={() => setOpen(true)}>
          {messages('label')}
          <InformationCircleIcon className="h-4 w-4" />
        </Button>
      </Popover.Trigger>

      <Popover.Content hideArrow align="start" side="bottom" onClose={() => setOpen(false)}>
        <Popover.Content.Heading>{messages('title')}</Popover.Content.Heading>
        <Popover.Content.Body className="text-sm leading-5 space-y-2 text-neutral">
          <span className="flex flex-col gap-2">{parseReactElement(messages('description'))}</span>
        </Popover.Content.Body>
      </Popover.Content>
    </Popover>
  );
};
