import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { TeamMemberSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { usePrefillPlan } from './usePrefillPlan';

type PrefillPlanModal = {
  open: boolean;
  onClose: () => void;
  teamMemberId: TeamMemberSchema['id'];
};

export const PrefillPlanModal: React.FC<PrefillPlanModal> = ({ open, onClose, teamMemberId }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.prefillPlan');

  const { canPrefillPlan, prefillPlan, prefill } = usePrefillPlan({
    teamMemberId,
  });

  const override = () => {
    prefill();
    onClose();
  };

  return (
    <ConfirmationModal.Root open={open} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="warning">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('modal.title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          {safeHTMLToReact(
            messages('modal.subtitle', {
              planName: prefillPlan?.title,
              planStartDate: prefillPlan?.startDate,
              planEndDate: prefillPlan?.endDate,
            })
          )}
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={onClose}>
          {messages('modal.cancelCTA')}
        </Button>
        <Button color="warning" variant="contained" size="md" onClick={override} disabled={!canPrefillPlan}>
          {messages('modal.overrideCTA')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
