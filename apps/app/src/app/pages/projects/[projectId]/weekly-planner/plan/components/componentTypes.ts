import React, { type ComponentType } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import type { TableCellProps } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import type { TableHeaderProps } from '@shape-construction/arch-ui/src/Table/components/TableHeader';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { PlaceholderPlanActivityStatus } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/fields/PlanActivityStatus/PlaceholderPlanActivityStatus';
import { PlanActivityStatus } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/fields/PlanActivityStatus/PlanActivityStatus';
import { ActivityNameCell } from '../../components/table/ActivityNameCell';
import { ActivityNameHeader } from '../../components/table/ActivityNameHeader';
import { OrganisationCell } from '../../components/table/OrganisationCell';
import { OrganisationHeader } from '../../components/table/OrganisationHeader';
import { ResponsiblePersonCell } from '../../components/table/ResponsiblePersonCell';
import { ResponsiblePersonHeader } from '../../components/table/ResponsiblePersonHeader';
import { ActivityName } from './fields/ActivityName/ActivityName';
import { ActivityNameSearchField } from './fields/ActivityName/ActivityNameSearchField';
import { ActivityReadinessRequirements } from './fields/ActivityReadinessRequirements/ActivityReadinessRequirements';
import { ActivityReady } from './fields/ActivityReady/ActivityReady';
import { PlaceholderActivityReady } from './fields/ActivityReady/PlaceholderActivityReady';
import { ActivityStatus } from './fields/ActivityStatus/ActivityStatus';
import { PlaceholderActivityStatus } from './fields/ActivityStatus/PlaceholderActivityStatus';
import { Comment } from './fields/Comment/Comment';
import { CurrentProgress } from './fields/CurrentProgress';
import { ExpectedProgress } from './fields/ExpectedProgress';
import { Location } from './fields/Location/Location';
import { PlaceholderLocation } from './fields/Location/PlaceholderLocation';
import { Organisation } from './fields/Organisation/Organisation';
import { PlaceholderOrganisation } from './fields/Organisation/PlaceholderOrganisation';
import { PlaceholderTextarea } from './fields/PlaceholderTextarea';
import { PlaceholderResponsiblePerson } from './fields/ResponsiblePerson/PlaceholderResponsiblePerson';
import { ResponsiblePerson } from './fields/ResponsiblePerson/ResponsiblePerson';
import { PlaceholderSchedule } from './fields/Schedule/PlaceholderSchedule';
import { Schedule } from './fields/Schedule/Schedule';
import type { FieldPropsWithPlan, PlaceholderComponentProps } from './fields/types';
import { PlaceholderProgressField } from './ProgressField/PlaceholderProgressField';

export const allColumns = [
  'activityName',
  'location',
  'organisation',
  'responsible',
  'status',
  'ready',
  'requirements',
  'planActivityStatus',
  'progress',
  'expectedProgress',
  'schedule',
  'comment',
];

type FieldOptions = {
  title: React.ReactNode;
  component: ComponentType<FieldPropsWithPlan>;
  placeholderComponent: ComponentType<PlaceholderComponentProps>;
  header?: ComponentType<TableHeaderProps>;
  cell?: ComponentType<TableCellProps>;
};

export const useColumnOptions = (plan?: WeeklyWorkPlanSchema) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.header');
  const expectedEndDate = plan?.endDate ? parseDateWithFormat(plan?.endDate, 'DD-MMM-YYYY') : '';

  const columnOptions: Record<string, FieldOptions> = {
    activityName: {
      title: messages('activityName'),
      component: ActivityName,
      placeholderComponent: ActivityNameSearchField,
      header: ActivityNameHeader,
      cell: ActivityNameCell,
    },
    location: {
      title: messages('location'),
      component: Location,
      placeholderComponent: PlaceholderLocation,
    },
    organisation: {
      title: messages('organisation'),
      component: Organisation,
      placeholderComponent: PlaceholderOrganisation,
      header: OrganisationHeader,
      cell: OrganisationCell,
    },
    responsible: {
      title: messages('responsible'),
      component: ResponsiblePerson,
      placeholderComponent: PlaceholderResponsiblePerson,
      header: ResponsiblePersonHeader,
      cell: ResponsiblePersonCell,
    },
    status: {
      title: messages('status'),
      component: ActivityStatus,
      placeholderComponent: PlaceholderActivityStatus,
    },
    ready: {
      title: messages('ready'),
      component: ActivityReady,
      placeholderComponent: PlaceholderActivityReady,
    },
    requirements: {
      title: messages('requirements'),
      component: ActivityReadinessRequirements,
      placeholderComponent: PlaceholderTextarea,
    },
    planActivityStatus: {
      title: messages('planActivityStatus'),
      component: PlanActivityStatus,
      placeholderComponent: PlaceholderPlanActivityStatus,
    },
    progress: {
      title: messages('progress'),
      component: CurrentProgress,
      placeholderComponent: PlaceholderProgressField,
    },
    expectedProgress: {
      title: safeHTMLToReact(messages('expectedProgressDate', { expectedEndDate })),
      component: ExpectedProgress,
      placeholderComponent: PlaceholderProgressField,
    },
    schedule: {
      title: messages('schedule'),
      component: Schedule,
      placeholderComponent: PlaceholderSchedule,
    },
    comment: {
      title: messages('comment'),
      component: Comment,
      placeholderComponent: PlaceholderTextarea,
    },
  };

  return { columnOptions };
};
