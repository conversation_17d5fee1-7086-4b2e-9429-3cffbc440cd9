import type { ComponentType } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import type { TableCellProps } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import type { TableHeaderProps } from '@shape-construction/arch-ui/src/Table/components/TableHeader';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { LookbackPlanActivityStatus } from 'app/pages/projects/[projectId]/weekly-planner/[planId]/lookback/components/fields/LookbackPlanActivityStatus/LookbackPlanActivityStatus';
import { ActivityNameCell } from '../../../components/table/ActivityNameCell';
import { ActivityNameHeader } from '../../../components/table/ActivityNameHeader';
import { OrganisationCell } from '../../../components/table/OrganisationCell';
import { OrganisationHeader } from '../../../components/table/OrganisationHeader';
import { ActivityNameSearchField } from '../../../plan/components/fields/ActivityName/ActivityNameSearchField';
import type { PlaceholderComponentProps } from '../../../plan/components/fields/types';
import { LookbackAchievedProgress } from './fields/LookbackAchievedProgress/LookbackAchievedProgress';
import { LookbackActivityName } from './fields/LookbackActivityName/LookbackActivityName';
import { LookbackActivityStatus } from './fields/LookbackActivityStatus/LookbackActivityStatus';
import { PlaceholderActivityStatus } from './fields/LookbackActivityStatus/PlaceholderActivityStatus';
import { LookbackComment } from './fields/LookbackComment/LookbackComment';
import { LookbackExpectedProgress } from './fields/LookbackExpectedProgress/LookbackExpectedProgress';
import { PlaceholderProgressField } from './fields/LookbackExpectedProgress/PlaceholderProgressField';
import { LookbackLocation } from './fields/LookbackLocation/LookbackLocation';
import { PlaceholderLocation } from './fields/LookbackLocation/PlaceholderLocation';
import { LookbackOrganisation } from './fields/LookbackOrganisation/LookbackOrganisation';
import { PlaceholderOrganisation } from './fields/LookbackOrganisation/PlaceholderOrganisation';
import { PlaceholderPlanActivityStatus } from './fields/LookbackPlanActivityStatus/PlaceholderPlanActivityStatus';
import { LookbackProgressLogs } from './fields/LookbackProgressLogs/LookbackProgressLogs';
import { PlaceholderProgressLogs } from './fields/LookbackProgressLogs/PlaceholderProgressLogs';
import { LookbackVarianceCategory } from './fields/LookbackVarianceCategory/LookbackVarianceCategory';
import { PlaceholderVarianceCategory } from './fields/LookbackVarianceCategory/PlaceholderVariance';
import { LookbackVarianceRecoveryMitigationMeasures } from './fields/LookbackVarianceRecoveryMitigationMeasures/LookbackVarianceRecoveryMitigationMeasures';
import { LookbackVarianceRemarks } from './fields/LookbackVarianceRemarks/LookbackVarianceRemarks';
import { PlaceholderTextarea } from './fields/LookbackVarianceRemarks/PlaceholderTextarea';
import type { LookbackFieldPropsWithPlan } from './fields/types';

export const lookbackColumns = [
  'activityName',
  'location',
  'organisation',
  'status',
  'planActivityStatus',
  'expectedProgress',
  'achievedProgress',
  'progressLogs',
  'varianceCategory',
  'varianceRemarks',
  'varianceRecoveryMitigationMeasures',
  'comment',
];

type FieldOptions = {
  title: React.ReactNode;
  component: ComponentType<LookbackFieldPropsWithPlan>;
  placeholderComponent: ComponentType<PlaceholderComponentProps>;
  header?: ComponentType<TableHeaderProps>;
  cell?: ComponentType<TableCellProps>;
};

export const useLookbackColumnOptions = (plan?: WeeklyWorkPlanSchema) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.lookback.header');
  const planEndDate = plan?.endDate ? parseDateWithFormat(plan?.endDate, 'DD-MMM-YYYY') : '';

  const lookbackColumnOptions: Record<string, FieldOptions> = {
    activityName: {
      title: messages('activityName'),
      component: LookbackActivityName,
      placeholderComponent: ActivityNameSearchField,
      header: ActivityNameHeader,
      cell: ActivityNameCell,
    },
    location: {
      title: messages('location'),
      component: LookbackLocation,
      placeholderComponent: PlaceholderLocation,
    },
    organisation: {
      title: messages('organisation'),
      component: LookbackOrganisation,
      placeholderComponent: PlaceholderOrganisation,
      header: OrganisationHeader,
      cell: OrganisationCell,
    },
    status: {
      title: messages('status'),
      component: LookbackActivityStatus,
      placeholderComponent: PlaceholderActivityStatus,
    },
    planActivityStatus: {
      title: messages('planActivityStatus'),
      component: LookbackPlanActivityStatus,
      placeholderComponent: PlaceholderPlanActivityStatus,
    },
    expectedProgress: {
      title: safeHTMLToReact(messages('expectedProgressDate', { planEndDate })),
      component: LookbackExpectedProgress,
      placeholderComponent: PlaceholderProgressField,
    },
    achievedProgress: {
      title: safeHTMLToReact(messages('achievedProgressDate', { planEndDate })),
      component: LookbackAchievedProgress,
      placeholderComponent: PlaceholderProgressField,
    },
    progressLogs: {
      title: messages('progressLogs'),
      component: LookbackProgressLogs,
      placeholderComponent: PlaceholderProgressLogs,
    },
    varianceCategory: {
      title: messages('varianceCategory'),
      component: LookbackVarianceCategory,
      placeholderComponent: PlaceholderVarianceCategory,
    },
    varianceRemarks: {
      title: messages('varianceRemarks'),
      component: LookbackVarianceRemarks,
      placeholderComponent: PlaceholderTextarea,
    },
    varianceRecoveryMitigationMeasures: {
      title: safeHTMLToReact(messages('varianceRecoveryMitigationMeasures')),
      component: LookbackVarianceRecoveryMitigationMeasures,
      placeholderComponent: PlaceholderTextarea,
    },
    comment: {
      title: messages('comment'),
      component: LookbackComment,
      placeholderComponent: PlaceholderTextarea,
    },
  };

  return { lookbackColumnOptions };
};
