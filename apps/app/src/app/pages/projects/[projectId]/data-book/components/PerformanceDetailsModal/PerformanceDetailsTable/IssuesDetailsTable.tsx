import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesSuspenseQueryOptions } from '@shape-construction/api/src/hooks';
import type { IssueSummarySchema, TeamMemberSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import EmptyState from '@shape-construction/arch-ui/src/EmptyState';
import { ShapeIssueTrackerIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useQuery } from '@tanstack/react-query';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { usePagination } from 'app/hooks/usePagination';
import { useProjectPersonGetter } from 'app/queries/projects/people';
import { Link } from 'react-router';
import { getLabelByQualityPercentage, qualityLabelBadgeThemeMap } from '../../../utils/quality-labels';

type IssuesDetailsTableProps = {
  endDate: string;
  startDate: string;
  teamMemberId: TeamMemberSchema['id'];
};

export const IssuesDetailsTable: React.FC<IssuesDetailsTableProps> = ({ endDate, startDate, teamMemberId }) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.performanceDetails.issuesTable');
  const paginationMessages = useMessageGetter('table.cursorPagination');
  const project = useCurrentProject();
  const { after, before, pageSize, onNext, onPrevious } = usePagination();
  const { data: issuesData, isLoading } = useQuery(
    getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesSuspenseQueryOptions(project.id, {
      date_start: startDate,
      date_end: endDate,
      team_member_id: teamMemberId,
      after,
      before,
      page_size: pageSize,
    })
  );

  if (isLoading || !issuesData) return <LoadingSpinner variant="screen" />;

  if (issuesData.meta.total === 0) {
    return (
      <EmptyState
        icon={<ShapeIssueTrackerIcon className="h-10 w-10" />}
        title={messages('emptyState.title')}
        body={messages('emptyState.body')}
      />
    );
  }

  const pagedMode = issuesData.meta.hasPreviousPage || issuesData.meta.hasNextPage;

  return (
    <div>
      <Table aria-label={messages('label')}>
        <Table.Heading className="max-sm:hidden">
          <Table.Row>
            <Table.Header className="normal-case">{messages('headers.title')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.currentResponsible')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.score')}</Table.Header>
          </Table.Row>
        </Table.Heading>

        <Table.Body>
          {issuesData.entries.map((issue) => (
            <IssuesDetailsRow key={issue.id} issue={issue} />
          ))}
        </Table.Body>

        {pagedMode && (
          <tfoot>
            <Table.Row>
              <Table.Cell colSpan={100}>
                <Pagination
                  {...issuesData.meta}
                  onNext={onNext}
                  onPrevious={onPrevious}
                  previousButtonLabel={paginationMessages('previous')}
                  nextButtonLabel={paginationMessages('next')}
                  resultsCountLabel={parseReactElement(
                    paginationMessages('resultsCount', {
                      count: issuesData.entries.length,
                      total: issuesData.meta.total,
                    })
                  )}
                />
              </Table.Cell>
            </Table.Row>
          </tfoot>
        )}
      </Table>
    </div>
  );
};

export const IssuesDetailsRow: React.FC<{ issue: IssueSummarySchema }> = ({ issue }) => {
  const project = useCurrentProject();
  const qualityLabelMessages = useMessageGetter('dataBook.page.heatmapDashboard.heatmap.qualityLabel');
  const qualityLabel = typeof issue?.qualityScore === 'number' ? getLabelByQualityPercentage(issue.qualityScore) : null;
  const getPerson = useProjectPersonGetter(project.id);

  return (
    <Table.Row
      striped
      className="[&>td]:text-neutral-bold border-b border-b-neutral-subtlest max-sm:grid max-sm:grid-cols-2 max-sm:[&>td]:py-2"
    >
      <Table.Cell className="whitespace-pre-wrap">
        <Link
          target="_blank"
          to={`/projects/${project.id}/issues/lists/all?issueId=${issue.id}`}
          className="text-link-brand underline hover:text-link-brand-hovered"
        >
          {issue?.title}
        </Link>
      </Table.Cell>

      <Table.Cell className="max-sm:hidden pl-4">
        <div className="max-sm:hidden text-xs leading-4 font-normal text-neutral-subtle">
          {getPerson(issue?.observerId)?.user.name ?? ''}
        </div>
      </Table.Cell>

      <Table.Cell className="pl-4">
        {qualityLabel && (
          <Badge label={qualityLabelMessages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
        )}
      </Table.Cell>
    </Table.Row>
  );
};
