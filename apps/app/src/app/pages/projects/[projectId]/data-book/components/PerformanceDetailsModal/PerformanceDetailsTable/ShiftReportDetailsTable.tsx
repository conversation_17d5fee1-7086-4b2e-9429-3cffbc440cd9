import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryOptions } from '@shape-construction/api/src/hooks';
import type { ShiftReportBasicDetailsSchema, TeamMemberSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import EmptyState from '@shape-construction/arch-ui/src/EmptyState';
import { ShapeShiftReportIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import { DATE_FORMAT, parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useQuery } from '@tanstack/react-query';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { usePagination } from 'app/hooks/usePagination';
import { Link } from 'react-router';
import { getLabelByQualityPercentage, qualityLabelBadgeThemeMap } from '../../../utils/quality-labels';

type ShiftReportDetailsTableProps = {
  endDate: string;
  startDate: string;
  teamMemberId: TeamMemberSchema['id'];
};

export const ShiftReportDetailsTable: React.FC<ShiftReportDetailsTableProps> = ({
  endDate,
  startDate,
  teamMemberId,
}) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable');
  const paginationMessages = useMessageGetter('table.cursorPagination');
  const project = useCurrentProject();
  const { after, before, pageSize, onNext, onPrevious } = usePagination();
  const { data: shiftReportsData, isLoading } = useQuery(
    getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryOptions(project.id, {
      date_start: startDate,
      date_end: endDate,
      team_member_id: teamMemberId,
      after,
      before,
      page_size: pageSize,
    })
  );

  if (isLoading || !shiftReportsData) return <LoadingSpinner variant="screen" />;

  if (shiftReportsData.meta.total === 0) {
    return (
      <EmptyState
        icon={<ShapeShiftReportIcon className="h-10 w-10" />}
        title={messages('emptyState.title')}
        body={messages('emptyState.body')}
      />
    );
  }

  const pagedMode = shiftReportsData.meta.hasPreviousPage || shiftReportsData.meta.hasNextPage;

  return (
    <div>
      <Table aria-label={messages('label')}>
        <Table.Heading className="max-sm:hidden">
          <Table.Row>
            <Table.Header className="normal-case">{messages('headers.document')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.date')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.shift')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.attachments')}</Table.Header>
            <Table.Header className="normal-case">{messages('headers.score')}</Table.Header>
          </Table.Row>
        </Table.Heading>

        <Table.Body>
          {shiftReportsData.entries.map((shiftReport) => (
            <ShiftReportDetailsRow key={shiftReport.id} shiftReport={shiftReport} />
          ))}
        </Table.Body>

        {pagedMode && (
          <tfoot>
            <Table.Row>
              <Table.Cell colSpan={100}>
                <Pagination
                  {...shiftReportsData.meta}
                  onNext={onNext}
                  onPrevious={onPrevious}
                  previousButtonLabel={paginationMessages('previous')}
                  nextButtonLabel={paginationMessages('next')}
                  resultsCountLabel={parseReactElement(
                    paginationMessages('resultsCount', {
                      count: shiftReportsData.entries.length,
                      total: shiftReportsData.meta.total,
                    })
                  )}
                />
              </Table.Cell>
            </Table.Row>
          </tfoot>
        )}
      </Table>
    </div>
  );
};

export const ShiftReportDetailsRow: React.FC<{ shiftReport: ShiftReportBasicDetailsSchema }> = ({ shiftReport }) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable');
  const qualityLabelMessages = useMessageGetter('dataBook.page.heatmapDashboard.heatmap.qualityLabel');
  const qualityLabel = shiftReport?.completionQualityScore
    ? getLabelByQualityPercentage(shiftReport.completionQualityScore)
    : null;

  const reportDate = useMemo(() => parseDateWithFormat(shiftReport.reportDate, DATE_FORMAT), [shiftReport]);

  return (
    <Table.Row
      striped
      className="[&>td]:text-neutral-bold border-b border-b-neutral-subtlest max-sm:grid max-sm:grid-cols-2 max-sm:[&>td]:py-2"
    >
      <Table.Cell className="min-w-0 truncate">
        <Link
          to={`/projects/${shiftReport.projectId}/shift-reports/${shiftReport.id}`}
          className="text-link-brand underline hover:text-link-brand-hovered"
          target="_blank"
        >
          {shiftReport?.reportTitle || messages('reportTitle', { reportDate: shiftReport?.reportDate })}
        </Link>
      </Table.Cell>

      <Table.Cell className="pl-4">
        <div className="sm:hidden text-xs leading-4 font-normal text-neutral-subtle">{messages('headers.date')}</div>
        {reportDate}
      </Table.Cell>

      <Table.Cell className="max-sm:hidden pl-4">{shiftReport.shiftType}</Table.Cell>

      <Table.Cell className="pl-4">
        <div className="sm:hidden text-xs leading-4 font-normal text-neutral-subtle">
          {messages('headers.attachments')}
        </div>
        {shiftReport.documentCount ?? 0}
      </Table.Cell>

      <Table.Cell className="pl-4">
        {qualityLabel && (
          <Badge label={qualityLabelMessages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
        )}
      </Table.Cell>
    </Table.Row>
  );
};
