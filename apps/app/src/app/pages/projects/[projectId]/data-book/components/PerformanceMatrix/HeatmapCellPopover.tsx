import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { DataHealthDashboardScoreSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { parseReactElement } from '@shape-construction/utils/dom';
import { getHeatmapColorClasses, HEATMAP_THEME } from './heatmap-config';
import { getHeatmapLevel } from './utils';

export type HeatmapCellPopoverProps = React.PropsWithChildren<{
  dataPoint: DataHealthDashboardScoreSchema;
}>;

export const HeatmapCellPopover = ({ dataPoint, children }: HeatmapCellPopoverProps) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.heatmap.cell.popover');
  const healthLevelMessages = useMessageGetter('dataBook.page.heatmapDashboard.healthLevels');
  const level = useMemo(() => getHeatmapLevel(dataPoint), [dataPoint]);
  const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');

  const renderPopoverContentBody = () => {
    if (dataPoint.recordCount === 0) {
      return <div>{messages('noPublishedRecord')}</div>;
    }

    return (
      <>
        <div>{parseReactElement(messages('publishedRecords', { recordCount: dataPoint.recordCount }))}</div>
        <div>{parseReactElement(messages('averageScore', { score: dataPoint.score }))}</div>

        <div className="pt-2">
          <Badge label={healthLevelMessages(`${level}.label`)} className={badgeClasses} />
        </div>
      </>
    );
  };

  return (
    <Tooltip.Root delayDuration={500}>
      <Tooltip.Trigger asChild>{children}</Tooltip.Trigger>
      <Tooltip.Content side="top" sideOffset={0} color="dark" className="px-3 py-2 max-md:w-auto">
        <div aria-label={messages('label')}>
          {renderPopoverContentBody()}
          <div className="text-neutral-subtlest text-xs pt-2">{messages('clickForDetails')}</div>
        </div>
      </Tooltip.Content>
    </Tooltip.Root>
  );
};
