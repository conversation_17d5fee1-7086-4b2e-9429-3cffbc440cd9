import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ResourceListSchema } from '@shape-construction/api/src/types';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { parseReactElement } from '@shape-construction/utils/dom';
import type { PaginationCursor } from 'app/hooks/usePagination';
import { ResourcesDropdown } from 'app/pages/projects/[projectId]/settings/resources/components/ResourceTable/ResourcesDropdown';
import type { ResourceTabId } from 'app/pages/projects/[projectId]/settings/resources/utils/constants';
import { ResourceStatusBadge } from './ResourceStatusBadge';

export type ResourceTableLargeProps = {
  resources: ResourceListSchema;
  tabId: ResourceTabId;
  onNext: (cursor: PaginationCursor) => void;
  onPrevious: (cursor: PaginationCursor) => void;
};

export const ResourceTableLarge: React.FC<ResourceTableLargeProps> = ({ resources, tabId, onNext, onPrevious }) => {
  const messages = useMessageGetter('admin.resources');
  const paginationMessages = useMessageGetter('table.cursorPagination');

  return (
    <Table.Container className="overflow-x-auto">
      <Table>
        <Table.Heading>
          <Table.Row>
            <Table.Header className="w-6/12">{messages(`${tabId}.nameColumn`)}</Table.Header>
            <Table.Header className="w-5/12">
              <div className="flex gap-2">
                <span>{messages('status.nameColumn')}</span>
                <Tooltip.Root>
                  <Tooltip.Trigger data-testid="status-tooltip-trigger">
                    <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
                  </Tooltip.Trigger>
                  <Tooltip.Content side="top" className="normal-case">
                    {messages('status.tooltip')}
                  </Tooltip.Content>
                </Tooltip.Root>
              </div>
            </Table.Header>
            <Table.Header className="w-1/12" />
          </Table.Row>
        </Table.Heading>

        <Table.Body className="[&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
          {resources?.entries.map((resource) => (
            <Table.Row key={resource.id}>
              <Table.Cell>
                <div className="text-gray-800">{resource.name}</div>
              </Table.Cell>
              <Table.Cell>
                <ResourceStatusBadge actionType={resource.disabled ? 'disable' : 'enable'} />
              </Table.Cell>
              <Table.Cell className="flex justify-end">
                <ResourcesDropdown actionType={resource.disabled ? 'enable' : 'disable'} resource={resource} />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>

        <tfoot>
          <Table.Row>
            <Table.Cell colSpan={100}>
              <Pagination
                {...resources.meta!}
                onNext={onNext}
                onPrevious={onPrevious}
                previousButtonLabel={paginationMessages('previous')}
                nextButtonLabel={paginationMessages('next')}
                resultsCountLabel={parseReactElement(
                  paginationMessages('resultsCount', {
                    count: resources.entries.length,
                    total: resources.meta?.total,
                  })
                )}
              />
            </Table.Cell>
          </Table.Row>
        </tfoot>
      </Table>
    </Table.Container>
  );
};
