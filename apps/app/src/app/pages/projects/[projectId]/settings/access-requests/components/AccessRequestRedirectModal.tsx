import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectAccessRequestSchema, TeamSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import Modal from '@shape-construction/arch-ui/src/Modal';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { safeHTMLToReact } from '@shape-construction/utils/dom';

type AccessRequestRedirectModalProps = {
  redirectTeamId?: TeamSchema['id'];
  request: ProjectAccessRequestSchema;
  availableTeams: TeamSchema[];
  onSetRedirectTeamId: (team: TeamSchema['id']) => void;
  onSubmit: () => void;
  onClose: () => void;
};

export const AccessRequestRedirectModal: React.FC<AccessRequestRedirectModalProps> = ({
  onClose,
  redirectTeamId,
  request,
  availableTeams,
  onSetRedirectTeamId,
  onSubmit,
}) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('admin.accessRequests.reallocate');
  const selectedTeam = availableTeams.find((team) => team.id === redirectTeamId);

  return (
    <Modal.Root open onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{messages('title')}</Modal.Title>
        <Modal.SubTitle>
          {safeHTMLToReact(messages('subtitle', { userName: request.user.name }), {
            sanitizerOptions: { ALLOWED_TAGS: ['b'] },
          })}
        </Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="p-4">
        <Select.Root value={redirectTeamId} onChange={onSetRedirectTeamId}>
          <Select.Trigger variant="bordered">
            <Select.Value placeholder={messages('selectPlaceholder')} value={selectedTeam?.displayName!} />
          </Select.Trigger>
          <Select.ResponsivePanel portal={isLargeScreen}>
            <Select.Options>
              {availableTeams.map((team) => (
                <Select.Option key={team.id} value={team.id}>
                  <Select.OptionText>{team.displayName}</Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.ResponsivePanel>
        </Select.Root>
      </Modal.Content>
      <Modal.Footer>
        <Button color="secondary" variant="contained" size="md" onClick={onClose}>
          {messages('cancelCTA')}
        </Button>
        <Button color="primary" variant="contained" size="md" onClick={onSubmit} disabled={!redirectTeamId}>
          {messages('confirmCTA')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};
