import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import * as List from '@shape-construction/arch-ui/src/List';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useInfiniteQuery, useSuspenseQuery } from '@tanstack/react-query';
import { InfiniteLoadWaypoints } from 'app/components/InfiniteLoadWaypoints/InfiniteLoadWaypoints';
import { QuotaLimitsBanner } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/QuotaLimitsBanner';
import { useQuotaLimits } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/useQuotaLimits';
import { getProjectsAccessRequestsInfiniteQueryOptions } from 'app/queries/projects/access-request';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { getProjectTeamQueryOptions } from 'app/queries/projects/teams';
import { useParams } from 'react-router';
import { AccessRequestCard } from './AccessRequestCard';

type Params = {
  projectId: string;
};

export const AccessRequestsList = () => {
  const { projectId } = useParams<Params>();
  const { isLimitReached, isLoading } = useQuotaLimits('usersPerTeam');
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId!));
  const { data: team } = useSuspenseQuery(getProjectTeamQueryOptions(projectId!, project?.currentTeamId!));

  const messages = useMessageGetter('admin.accessRequests.list');

  const {
    data: paginatedAccessRequests,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery(getProjectsAccessRequestsInfiniteQueryOptions(projectId!));

  const accessRequests = paginatedAccessRequests?.pages.flatMap(({ projectAccessRequests }) => projectAccessRequests);

  const isEmpty = accessRequests && accessRequests.length === 0;

  return (
    <div className="pb-16 text-left">
      {!isLoading && isLimitReached && <QuotaLimitsBanner className="md:-mx-4 md:-mt-2" />}

      {isEmpty ? (
        <p className="p-4">{parseReactElement(messages('noPendingRequests', { displayName: team?.displayName }))}</p>
      ) : (
        <p className="p-4">{parseReactElement(messages('hasPendingRequests', { displayName: team?.displayName }))}</p>
      )}
      <List.Root>
        {accessRequests?.map((request, index) => {
          const isFirst = index === 0;
          const isLast = index === accessRequests.length - 1;

          return (
            <InfiniteLoadWaypoints
              key={request.id}
              hasNextPage={hasNextPage}
              fetchNext={fetchNextPage}
              isFirst={isFirst}
              isLast={isLast}
            >
              {team && <AccessRequestCard request={request} currentTeam={team} />}
            </InfiniteLoadWaypoints>
          );
        })}
      </List.Root>
    </div>
  );
};
