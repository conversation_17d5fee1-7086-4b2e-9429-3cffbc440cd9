import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  ProjectAccessRequestSchema,
  ProjectSchema,
  TeamSchema,
  TeamSubscriptionPlanSchema,
} from '@shape-construction/api/src/types';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Button from '@shape-construction/arch-ui/src/Button';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { CheckIcon, ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { ACCESS_REQUEST_ACTIONS } from 'app/constants/AccessRequests';
import { useTeamsSubscriptionPlan } from 'app/queries/teamsSubscriptionPlan/teamsSubscriptionPlan';
import { currencyFormatter } from 'libs/i18n/currencyFormatter';
import { useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
};

export interface AccessRequestActionModalProps {
  action: (typeof ACCESS_REQUEST_ACTIONS)[keyof typeof ACCESS_REQUEST_ACTIONS];
  team: TeamSchema;
  request: ProjectAccessRequestSchema;
  onClose: () => void;
  onSubmitAccept: () => void;
  onSubmitReject: () => void;
}

export const AccessRequestActionModal = ({
  action,
  team,
  request,
  onClose,
  onSubmitAccept,
  onSubmitReject,
}: AccessRequestActionModalProps) => {
  const { projectId } = useParams() as Params;
  const { data: teamSubscriptionData, isLoading: isLoadingTeamSubscription } = useTeamsSubscriptionPlan(
    projectId,
    team.id
  );

  const accepted = action === ACCESS_REQUEST_ACTIONS.ACCEPT;

  if (isLoadingTeamSubscription && !teamSubscriptionData) return null;

  return (
    <>
      {accepted && teamSubscriptionData?.billing && (
        <BillingAcceptModal
          userName={request.user.name}
          teamName={team.displayName || ''}
          onConfirm={onSubmitAccept}
          onCancel={onClose}
          billing={teamSubscriptionData?.billing}
        />
      )}
      {accepted && !teamSubscriptionData?.billing && (
        <AcceptModal
          userName={request.user.name}
          teamName={team.displayName || ''}
          onConfirm={onSubmitAccept}
          onCancel={onClose}
        />
      )}
      {!accepted && (
        <RejectModal
          userName={request.user.name}
          teamName={team.displayName || ''}
          onConfirm={onSubmitReject}
          onCancel={onClose}
        />
      )}
    </>
  );
};

interface ModalProps {
  userName: string;
  teamName: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const RejectModal = ({ userName, teamName, onConfirm, onCancel }: ModalProps) => {
  const messages = useMessageGetter('admin.accessRequests.reject');

  return (
    <ConfirmationModal.Root open onClose={onCancel}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          {safeHTMLToReact(messages('subtitle', { userName, teamName }), {
            sanitizerOptions: { ALLOWED_TAGS: ['b'] },
          })}
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button aria-label={messages('cancelCTA')} color="secondary" variant="outlined" size="sm" onClick={onCancel}>
          {messages('cancelCTA')}
        </Button>
        <Button
          color="danger"
          variant="contained"
          size="sm"
          disabled={false}
          onClick={onConfirm}
          aria-label={messages('confirmCTA')}
        >
          {messages('confirmCTA')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};

const AcceptModal = ({ userName, teamName, onConfirm, onCancel }: ModalProps) => {
  const messages = useMessageGetter('admin.accessRequests.accept');

  return (
    <ConfirmationModal.Root open onClose={() => {}}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="success">
            <CheckIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          {safeHTMLToReact(messages('subtitle', { userName, teamName }), {
            sanitizerOptions: { ALLOWED_TAGS: ['b'] },
          })}
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button aria-label={messages('cancelCTA')} color="secondary" variant="outlined" size="sm" onClick={onCancel}>
          {messages('cancelCTA')}
        </Button>
        <Button
          color="success"
          variant="contained"
          size="sm"
          disabled={false}
          onClick={onConfirm}
          aria-label={messages('confirmCTA')}
        >
          {messages('confirmCTA')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};

const BillingAcceptModal = ({
  userName,
  teamName,
  onConfirm,
  onCancel,
  billing,
}: ModalProps & { billing: NonNullable<TeamSubscriptionPlanSchema['billing']> }) => {
  const messages = useMessageGetter('admin.accessRequests.accept');

  const userLicenseCost = currencyFormatter(billing.currency).format(billing.costPerUserInCents / 100);

  return (
    <Modal.Root open onClose={onCancel}>
      <Modal.Header onClose={onCancel}>
        <Modal.Title>{messages('title')}</Modal.Title>
      </Modal.Header>
      <Modal.Content>
        <div className="my-6 flex flex-col gap-y-6">
          <Alert color="warning" justifyContent="start">
            <Alert.Message>{messages('billing.warning')}</Alert.Message>
          </Alert>
          <p>
            {safeHTMLToReact(messages('subtitle', { userName, teamName }), {
              sanitizerOptions: { ALLOWED_TAGS: ['b'] },
            })}{' '}
            {messages('billing.info', {
              COUNT: 1,
            })}
          </p>

          <div className="rounded-sm border p-6">
            <div className="flex justify-between">
              <dl className="flex flex-wrap gap-2.5">
                <dt className="font-medium">{messages('billing.newLicense.term')}</dt>
                <dd className="text-gray-500">
                  {messages('billing.newLicense.details', {
                    pricePerLicense: userLicenseCost,
                    numberOfLicenses: 1,
                  })}
                </dd>
              </dl>
              <span className="font-medium">{userLicenseCost}</span>
            </div>
          </div>
        </div>
      </Modal.Content>
      <Modal.Footer>
        <Button aria-label={messages('cancelCTA')} color="secondary" variant="outlined" size="sm" onClick={onCancel}>
          {messages('cancelCTA')}
        </Button>
        <Button
          color="primary"
          variant="contained"
          size="sm"
          disabled={false}
          onClick={onConfirm}
          aria-label={messages('confirmCTA')}
          data-testid="res-accept-button"
        >
          {messages('confirmCTA')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};
