import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectAccessRequestSchema, TeamSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import Button from '@shape-construction/arch-ui/src/Button';
import { DetailItem } from '@shape-construction/arch-ui/src/DetailItem';
import { ArrowsRightLeftIcon, InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import ModalBase from '@shape-construction/arch-ui/src/ModalBase';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import type { ConstructionRoleDetail } from 'app/components/People/constructionRoles/types';
import { generateRoleOptions, type RoleOption } from 'app/components/People/utils';
import { QuotaLimitsBanner } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/QuotaLimitsBanner';
import { useQuotaLimits } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/useQuotaLimits';
import { ACCESS_REQUEST_ACTIONS } from 'app/constants/AccessRequests';
import { DEFAULT_PERMISSION } from 'app/constants/Permissions';
import {
  useAcceptProjectAccessRequest,
  useProjectAccessRequestRedirect,
  useRejectProjectAccessRequest,
} from 'app/queries/projects/access-request';
import { getProjectQueryOptions, getProjectTeamMemberRoleQueryOptions } from 'app/queries/projects/projects';
import { getProjectTeamsQueryOptions } from 'app/queries/projects/teams';
import { useParams } from 'react-router';
import { AccessRequestActionModal } from './AccessRequestActionModal';
import { AccessRequestRedirectModal } from './AccessRequestRedirectModal';

type Params = {
  projectId: string;
};

export type AccessRequestScreenProps = {
  onClose: () => void;
  request: ProjectAccessRequestSchema;
};

export const AccessRequestScreen: React.FC<AccessRequestScreenProps> = ({ request, onClose }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { projectId } = useParams<Params>() as Params;
  const [actionRequest, setActionRequest] = useState<string | null>(null);
  const [redirectTeamId, setRedirectTeamId] = useState<TeamSchema['id']>();
  const [constructionRole, setConstructionRole] = useState({} as ConstructionRoleDetail);
  const [role, setRole] = useState({} as RoleOption);
  const [redirectRequest, setRedirectRequest] = useState(false);
  const acceptAccessRequest = useAcceptProjectAccessRequest();
  const rejectAccessRequest = useRejectProjectAccessRequest();
  const accessRequestRedirect = useProjectAccessRequestRedirect();
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId));
  const { data: currentRole } = useQuery(getProjectTeamMemberRoleQueryOptions(projectId));
  const { data: teamsInProject } = useQuery(getProjectTeamsQueryOptions(projectId));
  const { constructionRoleOptions, isLoading: isLoadingRoles } = useConstructionRoles({
    includePlaceholderOption: false,
  });
  const { isLimitReached, isLoading } = useQuotaLimits('usersPerTeam');

  const currentTeamId = project?.currentTeamId;
  const currentTeam = teamsInProject?.find(({ id }) => id === currentTeamId);
  const availableTeams = useMemo(() => {
    return teamsInProject?.filter((team) => team.id !== currentTeamId) || [];
  }, [currentTeamId, teamsInProject]);
  const permissionOptions = useMemo(() => generateRoleOptions(currentRole, false), [currentRole]);
  const messages = useMessageGetter('admin.accessRequests.review');

  const submitAcceptRequest = () => {
    acceptAccessRequest.mutate({
      projectId,
      projectAccessRequestId: request.id,
      data: {
        construction_role: constructionRole.value,
        role: role.value,
      },
    });
  };

  const submitRejectRequest = () => {
    rejectAccessRequest.mutate({ projectId, projectAccessRequestId: request.id });
    onClose();
  };

  const submitRedirect = () => {
    accessRequestRedirect.mutate({
      projectId,
      projectAccessRequestId: request.id,
      data: { team_id: redirectTeamId! },
    });
    onClose();
  };

  const changeRoleBasedOnConstructionRole = (roleDetail: ConstructionRoleDetail) => {
    setConstructionRole(roleDetail);

    if (!role?.value) {
      const roleValue = permissionOptions.find((option) => option.value === DEFAULT_PERMISSION);
      if (roleValue) {
        setRole(roleValue);
      }
    }
  };

  if (isLoading || isLoadingRoles || !currentTeam) return null;

  const isRedirectedFromTeam = Boolean(request.redirectedFromTeam);

  return (
    <>
      {!actionRequest && !redirectRequest && (
        <ModalBase.Root
          open
          fullWidth
          onClose={onClose}
          fullScreen={!isLargeScreen}
          maxWidth={isLargeScreen ? '3xl' : 'none'}
          roundBorders={isLargeScreen}
          outsidePad={isLargeScreen}
        >
          <ModalBase.Header bottomBorder onClose={onClose}>
            <ModalBase.Title>{messages('title')}</ModalBase.Title>
          </ModalBase.Header>

          <ModalBase.Content>
            {!isLoading && isLimitReached && <QuotaLimitsBanner className="-mx-6 mb-4" />}

            <p className="text-sm md:text-base my-6">
              {parseReactElement(
                messages(isRedirectedFromTeam ? 'redirected' : 'routed', {
                  team: currentTeam.displayName,
                })
              )}
            </p>
            <div
              className={cn('border rounded-md relative p-3', {
                'border-t-[25px] border-t-info-subtlest': isRedirectedFromTeam,
              })}
            >
              <p className="text-xs leading-7 text-neutral-subtle">From</p>
              <div className="flex items-center my-2">
                <div className="mr-3">
                  <Avatar size="md" text={request.user.name} imgURL={request.user.avatarUrl} />
                </div>
                <div>
                  <p className="text-base">{request.user.name}</p>
                  <p className="text-xs leading-7 text-neutral-subtle">{request.user.email}</p>
                </div>
              </div>
              <p className="text-xs leading-7 text-neutral-subtle">{messages('organisationRequested')}</p>
              <p className="text-base mb-4">{request.teamName}</p>
              <div>
                <p className="text-xs leading-7 text-neutral-subtle">{messages('message')}</p>
                <p className="text-base">{request.message ? request.message : messages('noMessage')}</p>
              </div>
              {isRedirectedFromTeam && (
                <div className="flex items-center absolute -top-6">
                  <ArrowsRightLeftIcon className="w-4 h-4 text-icon-info mr-1" />
                  <p className="text-xs leading-7 text-brand">
                    {parseReactElement(
                      messages('reRoutedFrom', {
                        team: request.redirectedFromTeam?.displayName,
                      })
                    )}
                  </p>
                </div>
              )}
            </div>
            {!isLimitReached && (
              <>
                <p className="text-sm md:text-base mt-6 mb-2">{messages('defineRolesAndPermissions')}</p>
                <Select.Root value={constructionRole} onChange={changeRoleBasedOnConstructionRole}>
                  <Select.Trigger variant="bordered">
                    <Select.Value
                      placeholder={messages('constructionRole.placeholder')}
                      value={constructionRole.label}
                    />
                  </Select.Trigger>
                  <Select.ResponsivePanel>
                    <Select.Options>
                      {constructionRoleOptions.map((roleDetail) => (
                        <Select.Option key={roleDetail.value} value={roleDetail}>
                          <Select.OptionText>{roleDetail.label}</Select.OptionText>
                        </Select.Option>
                      ))}
                    </Select.Options>
                  </Select.ResponsivePanel>
                </Select.Root>
                <Select.Root className="my-3" value={role} onChange={setRole}>
                  <Select.Trigger variant="bordered">
                    <Select.Value placeholder={messages('permissionRole.placeholder')} value={role.label} />
                  </Select.Trigger>
                  <Select.ResponsivePanel>
                    <Select.Options>
                      {permissionOptions.map((roleDetail) => (
                        <Select.Option key={roleDetail.value} value={roleDetail} disabled={roleDetail.disabled}>
                          <Select.OptionText>{roleDetail.label}</Select.OptionText>
                        </Select.Option>
                      ))}
                    </Select.Options>
                  </Select.ResponsivePanel>
                </Select.Root>
              </>
            )}
          </ModalBase.Content>

          {Boolean(availableTeams.length) && (
            <DetailItem
              className="mx-4"
              expandable={false}
              title={messages('differentTeam.caption')}
              onClick={() => setRedirectRequest(true)}
              icon={<InformationCircleIcon className="h-5 w-5 text-blue-400" />}
            />
          )}

          <ModalBase.Footer topBorder>
            <div className="flex justify-between items-center w-full">
              {availableTeams.length ? (
                <Button color="secondary" variant="outlined" size="md" onClick={() => setRedirectRequest(true)}>
                  {messages('differentTeam.switchTeamCTA')}
                </Button>
              ) : (
                <span />
              )}
              <div className="flex space-x-4">
                <Button
                  color="danger"
                  variant="contained"
                  size="md"
                  onClick={() => setActionRequest(ACCESS_REQUEST_ACTIONS.REJECT)}
                >
                  {messages('cancelCTA')}
                </Button>

                <Button
                  color="primary"
                  variant="contained"
                  size="md"
                  onClick={() => setActionRequest(ACCESS_REQUEST_ACTIONS.ACCEPT)}
                  disabled={isLimitReached || !constructionRole?.value || !role?.value}
                >
                  {messages('confirmCTA')}
                </Button>
              </div>
            </div>
          </ModalBase.Footer>
        </ModalBase.Root>
      )}

      {actionRequest && (
        <AccessRequestActionModal
          team={currentTeam}
          action={actionRequest}
          onClose={() => setActionRequest(null)}
          request={request}
          onSubmitAccept={() => submitAcceptRequest()}
          onSubmitReject={() => submitRejectRequest()}
        />
      )}
      {redirectRequest && (
        <AccessRequestRedirectModal
          onClose={() => setRedirectRequest(false)}
          onSubmit={submitRedirect}
          availableTeams={availableTeams}
          request={request}
          onSetRedirectTeamId={setRedirectTeamId}
          redirectTeamId={redirectTeamId}
        />
      )}
    </>
  );
};
