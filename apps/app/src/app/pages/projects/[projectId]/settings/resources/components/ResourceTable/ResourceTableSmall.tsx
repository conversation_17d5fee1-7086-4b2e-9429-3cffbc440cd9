import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ResourceListSchema } from '@shape-construction/api/src/types';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import { parseReactElement } from '@shape-construction/utils/dom';
import type { PaginationCursor } from 'app/hooks/usePagination';
import { ResourcesDropdown } from 'app/pages/projects/[projectId]/settings/resources/components/ResourceTable/ResourcesDropdown';
import type { ResourceTabId } from 'app/pages/projects/[projectId]/settings/resources/utils/constants';
import { ResourceStatusBadge } from './ResourceStatusBadge';

export type ResourceTableSmallProps = {
  resources: ResourceListSchema;
  tabId: ResourceTabId;
  onNext: (cursor: PaginationCursor) => void;
  onPrevious: (cursor: PaginationCursor) => void;
};

export const ResourceTableSmall: React.FC<ResourceTableSmallProps> = ({ resources, tabId, onNext, onPrevious }) => {
  const messages = useMessageGetter('admin.resources');
  const paginationMessages = useMessageGetter('table.cursorPagination');

  return (
    <Table.Container className="overflow-x-auto">
      <Table>
        <Table.Heading>
          <Table.Row>
            <Table.Header>{messages(`${tabId}.nameColumn`)}</Table.Header>
          </Table.Row>
        </Table.Heading>

        <Table.Body className="[&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
          {resources?.entries.map((resource) => (
            <Table.Row key={resource.id}>
              <Table.Cell className="flex justify-between items-start">
                <div className="flex flex-col gap-2 w-10/12">
                  <div className="text-gray-800">{resource.name}</div>
                  <div>
                    <ResourceStatusBadge actionType={resource.disabled ? 'disable' : 'enable'} />
                  </div>
                </div>
                <ResourcesDropdown actionType={resource.disabled ? 'enable' : 'disable'} resource={resource} />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>

        <tfoot>
          <Table.Row>
            <Table.Cell colSpan={100}>
              <Pagination
                {...resources.meta!}
                onNext={onNext}
                onPrevious={onPrevious}
                previousButtonLabel={paginationMessages('previous')}
                nextButtonLabel={paginationMessages('next')}
                resultsCountLabel={parseReactElement(
                  paginationMessages('resultsCount', {
                    count: resources.entries.length,
                    total: resources.meta?.total,
                  })
                )}
              />
            </Table.Cell>
          </Table.Row>
        </tfoot>
      </Table>
    </Table.Container>
  );
};
