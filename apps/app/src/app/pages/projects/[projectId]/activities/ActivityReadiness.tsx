import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import {
  getApiProjectsProjectIdPeopleTeamMemberIdQueryOptions,
  getApiProjectsProjectIdQueryOptions,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryOptions,
} from '@shape-construction/api/src/hooks';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar/Avatar';
import Button from '@shape-construction/arch-ui/src/Button';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { ArrowsPointingOutIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { formatDate } from '@shape-construction/utils/DateTime';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useQuery } from '@tanstack/react-query';
import { Link, useParams } from 'react-router';
import { ActivityBlockers } from './[activityId]/components/ActivityBlockers/ActivityBlockers';
import { ActivityReadinessAction } from './components/ActivityReadinessAction';
import { ActivityReadinessRequirementsList } from './components/ActivityReadinessRequirementsList';
import { ActivityReadinessStateBadge } from './components/ActivityReadinessStateBadge';
import { Widget } from './components/Widget';

type Params = {
  projectId: string;
  shiftActivityId: string;
};

export type ActivityReadinessProps = {
  expanded?: boolean;
};

export const ActivityReadiness: React.FC<ActivityReadinessProps> = ({ expanded }) => {
  const { projectId, shiftActivityId } = useParams() as Params;
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('shiftManager.activities.readiness');
  const { data: project } = useQuery(getApiProjectsProjectIdQueryOptions(projectId));
  const {
    data: shiftActivity,
    isLoading,
    isError,
  } = useQuery(getApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryOptions(projectId, shiftActivityId));
  const { data: teamMember } = useQuery(
    getApiProjectsProjectIdPeopleTeamMemberIdQueryOptions(projectId, shiftActivity?.readiness.updaterId!)
  );

  if (!shiftActivity) return null;

  return (
    <Widget
      actions={
        <div className="flex flex-row gap-1">
          <ActivityReadinessAction shiftActivity={shiftActivity} />
          {!expanded && (
            <Link to={`/projects/${projectId}/activities/${shiftActivityId}/readiness`}>
              <Button
                aria-label={messages('actions.seeAll')}
                size="xxs"
                color="secondary"
                variant="outlined"
                leadingIcon={ArrowsPointingOutIcon}
              >
                {messages('actions.seeAll')}
              </Button>
            </Link>
          )}
        </div>
      }
      title={
        <div className="flex flex-row items-center gap-2">
          {messages('title')}
          <ActivityReadinessStateBadge shiftActivity={shiftActivity} />
        </div>
      }
      isLoading={isLoading}
      isError={isError}
    >
      <div className="flex flex-col gap-4">
        {shiftActivity?.readiness.updatedAt && (
          <div className="flex flex-row items-center gap-1">
            <Avatar size="xs" text={teamMember?.user.name ?? ''} imgURL={teamMember?.user.avatarUrl} />
            <span className="text-xs leading-4 font-normal text-neutral">
              {parseReactElement(
                messages('updaterStatus', {
                  teamMember: teamMember?.user.name,
                  ready: shiftActivity?.readiness.ready,
                  date: formatDate(shiftActivity?.readiness.updatedAt!, project?.timezone!),
                })
              )}
            </span>
          </div>
        )}
        <div className="px-4 py-4 flex flex-col md:flex-row bg-surface-overlay border border-neutral-subtlest rounded-md">
          <div className="md:w-1/2">
            <ActivityReadinessRequirementsList shiftActivity={shiftActivity} />
          </div>
          <Divider orientation={isLargeScreen ? 'vertical' : 'horizontal'} />
          <div className="md:w-1/2">
            <ActivityBlockers shiftActivity={shiftActivity} />
          </div>
        </div>
      </div>
    </Widget>
  );
};
