import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import FeedbackPanel from '@shape-construction/arch-ui/src/FeedbackPanel';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { AuthSplitScreen } from 'app/components/AuthSplitScreen/AuthSplitScreen';
import { OtpConfirmationForm } from 'app/components/OtpConfirmationForm/OtpConfirmationForm';
import { Link } from 'app/components/UI/Link/Link';
import ShapeLogo from 'app/components/UI/ShapeLogo/ShapeLogo';
import { environment } from 'app/config/environment';
import { useResendConfirmationEmail } from 'app/queries/users/users';
import { Navigate, useLocation, useNavigate, useSearchParams } from 'react-router';

export const ConfirmEmail: React.FC = () => {
  const confirmEmailMessages = useMessageGetter('auth.confirmEmail');
  const navigate = useNavigate();
  const { mutate: resendConfirmationEmail, isIdle } = useResendConfirmationEmail();
  const wasResendRequested = !isIdle;
  const [isLockedForConfirmation, setIsLockedForConfirmation] = useState(false);

  const [searchParams] = useSearchParams();
  const referrerPath = searchParams.get('referrer-path');

  const location = useLocation();
  const email = location.state?.unconfirmedEmail;
  const userId = location.state?.userId;

  const logInUrl = '/auth';
  const redirectTo = referrerPath ? decodeURIComponent(referrerPath) : logInUrl;
  const supportEmail = environment.SUPPORT_EMAIL;

  const handleBackToLogin = () => {
    navigate(logInUrl);
  };

  const subtitle = safeHTMLToReact(confirmEmailMessages('subtitle', { email }), {
    sanitizerOptions: {
      ALLOWED_TAGS: ['b'],
    },
  });

  if (!userId && !email) return <Navigate to={logInUrl} />;

  return (
    <AuthSplitScreen data-cy="confirm-email-page">
      <FeedbackPanel
        title={confirmEmailMessages('title')}
        subtitle={subtitle}
        footer={
          <>
            {confirmEmailMessages('backToLogin.backTo')}{' '}
            <Link to={logInUrl} onClick={handleBackToLogin}>
              {confirmEmailMessages('backToLogin.loginLink')}
            </Link>
          </>
        }
        logo={<ShapeLogo />}
      >
        <div className="mt-8">
          <OtpConfirmationForm
            userId={userId}
            onSuccess={() => navigate(redirectTo)}
            onLockedForConfirmation={() => setIsLockedForConfirmation(true)}
          />
          <div className="mt-8 text-center text-sm text-gray-600">
            {confirmEmailMessages('resendEmail.question')}
            <div className="mt-2">
              <Button
                color="primary"
                variant="outlined"
                size="md"
                onClick={() => resendConfirmationEmail({ data: { email } })}
                fullWidth
                disabled={wasResendRequested || isLockedForConfirmation}
              >
                {confirmEmailMessages('resendEmail.resendButton')}
              </Button>
            </div>
            <div className="mt-9">
              {confirmEmailMessages('support')}{' '}
              <a
                href={`mailto:${supportEmail}`}
                target="_blank"
                rel="noreferrer"
                className="font-medium text-indigo-500 hover:text-indigo-400"
              >
                {confirmEmailMessages('contactUs')}
              </a>
            </div>
          </div>
        </div>
      </FeedbackPanel>
    </AuthSplitScreen>
  );
};

export { ConfirmEmail as Component };
