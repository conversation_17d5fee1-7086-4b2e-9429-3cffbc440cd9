import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportQualityIndicatorsSchema } from '@shape-construction/api/src/types';
import Popover from '@shape-construction/arch-ui/src/Popover';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import type { ProgressBarActiveColor } from '@shape-construction/arch-ui/src/ProgressBar/ProgressBar';
import { getLabelByReportQualityPercentage, type ReportQualityLabels } from '../getLabelByReportQualityPercentage';
import { ShiftReportQualityDetails } from '../ShiftReportQualityDetails/ShiftReportQualityDetails';

const reportQualityLabelColorMap: Record<ReportQualityLabels, ProgressBarActiveColor> = {
  notuseful: 'danger',
  thebasics: 'warning',
  good: 'primary',
  verygood: 'success',
  comprehensive: 'success',
};

type ShiftReportQualityProgressBarProps = {
  qualityIndicators: ShiftReportQualityIndicatorsSchema;
};

export const ShiftReportQualityProgressBar: React.FC<ShiftReportQualityProgressBarProps> = ({ qualityIndicators }) => {
  const messages = useMessageGetter('shiftReport.qualityLabel');
  const currentProgress = qualityIndicators.currentScore.percentage.completed;
  const qualityLabel = getLabelByReportQualityPercentage(currentProgress) || 'notuseful';

  return (
    <Popover.Root>
      <Popover.Trigger>
        <ProgressBar.Root progress={currentProgress} color={reportQualityLabelColorMap[qualityLabel]}>
          <ProgressBar.Header showProgress>
            <ProgressBar.Title>{messages('title')}</ProgressBar.Title>
          </ProgressBar.Header>
        </ProgressBar.Root>
      </Popover.Trigger>
      <Popover.Content side="bottom" align="start" sideOffset={10} className="p-0">
        <Popover.Content.Body className="px-3 py-1">
          <ShiftReportQualityDetails qualityIndicators={qualityIndicators} />
        </Popover.Content.Body>
      </Popover.Content>
    </Popover.Root>
  );
};
