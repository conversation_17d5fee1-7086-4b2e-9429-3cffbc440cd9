import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportQualityIndicatorsSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { QualityDetailsListGroup } from 'app/components/QualityDetails/QualityDetailsListGroup';
import { QualityDetailsListProgressBar } from 'app/components/QualityDetails/QualityDetailsListProgressBar';
import { QualityDetailsListText } from 'app/components/QualityDetails/QualityDetailsListText';
import { getLabelByReportQualityPercentage } from '../getLabelByReportQualityPercentage';
import { qualityLabelBadgeThemeMap } from '../ShiftReportQualityIndicatorsInfoTable';

type ShiftReportQualityDetailsProps = {
  qualityIndicators: ShiftReportQualityIndicatorsSchema;
};

export const ShiftReportQualityDetails: React.FC<ShiftReportQualityDetailsProps> = ({ qualityIndicators }) => {
  const progressPopoverMessages = useMessageGetter('shiftReport.progressPopover');
  const qualityLabelMessages = useMessageGetter('shiftReport.qualityLabel');

  const currentScore = qualityIndicators.currentScore;
  const isDisabled = currentScore.percentage.completed === 0;

  const basics = qualityIndicators.basics;
  const basicsProgress = basics.percentage;
  const basicsItems = basics.items;
  const basicsProgressFinished = basicsProgress.completed === basicsProgress.total;

  const people = qualityIndicators.people;
  const peopleProgress = people.percentage;
  const peopleProgressFinished = peopleProgress.completed === peopleProgress.total;

  const equipment = qualityIndicators.equipment;
  const equipmentProgress = equipment.percentage;
  const equipmentProgressFinished = equipmentProgress.completed === equipmentProgress.total;

  const material = qualityIndicators.material;
  const materialProgress = material.percentage;
  const materialProgressFinished = material.percentage.completed === material.percentage.total;

  const evidence = qualityIndicators.evidence;
  const evidenceProgress = evidence.percentage;
  const evidenceItems = evidence.items;
  const evidenceFinished = evidenceProgress.completed === evidenceProgress.total;
  const evidenceItemsPercentage = evidenceItems.completed ? (evidenceItems.completed * 100) / evidenceItems.total : 0;

  const allocations = qualityIndicators.allocations;
  const allocationsProgress = allocations.percentage;
  const allocationsItems = allocations.items;
  const allocationsProgressFinished = allocationsProgress.completed === allocationsProgress.total;
  const allocationsItemsPercentage = allocationsItems.completed
    ? (allocationsItems.completed * 100) / allocationsItems.total
    : 0;

  const qualityLabel = getLabelByReportQualityPercentage(currentScore.percentage.completed) ?? 'notuseful';

  return (
    <div role="list">
      <div className="w-full">
        <div className="flex py-2 justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">{progressPopoverMessages('currentScore')}</div>
          <Badge label={qualityLabelMessages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
        </div>
      </div>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('basics.title')}
        progressPercentagesDescription={progressPopoverMessages('basics.titleInfo', {
          complete: basicsProgress.completed,
          total: basicsProgress.total,
        })}
        isComplete={basicsProgressFinished}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('basics.addReportDate')}
          isComplete={basicsItems.reportDate}
        />
        <QualityDetailsListText
          text={progressPopoverMessages('basics.addReportTitle')}
          isComplete={basicsItems.reportTitle}
        />
        <QualityDetailsListText
          text={progressPopoverMessages('basics.addWeatherDescription')}
          isComplete={basicsItems.weatherDescription}
        />
        <QualityDetailsListText
          text={progressPopoverMessages('basics.addProgressDowntime')}
          isComplete={basicsItems.activityOrDowntime}
        />
        {isDisabled && (
          <div className="flex py-2 space-x-1">
            <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
            <p
              key={progressPopoverMessages('info.description')}
              className="text-xs leading-4 font-normal text-blue-700"
            >
              {progressPopoverMessages('info.description')}
            </p>
          </div>
        )}
      </QualityDetailsListGroup>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('people.title')}
        progressPercentagesDescription={progressPopoverMessages('people.titleInfo', {
          complete: peopleProgress.completed,
          total: peopleProgress.total,
        })}
        isComplete={peopleProgressFinished}
        isDisabled={isDisabled}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('people.addPeopleToReport')}
          isComplete={peopleProgressFinished}
          isDisabled={isDisabled}
        />
      </QualityDetailsListGroup>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('equipment.title')}
        progressPercentagesDescription={progressPopoverMessages('equipment.titleInfo', {
          complete: equipmentProgress.completed,
          total: equipmentProgress.total,
        })}
        isComplete={equipmentProgressFinished}
        isDisabled={isDisabled}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('equipment.addEquipmentToReport')}
          isComplete={equipmentProgressFinished}
          isDisabled={isDisabled}
        />
      </QualityDetailsListGroup>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('material.title')}
        progressPercentagesDescription={progressPopoverMessages('material.titleInfo', {
          complete: materialProgress.completed,
          total: materialProgress.total,
        })}
        isComplete={materialProgressFinished}
        isDisabled={isDisabled}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('material.addMaterialToReport')}
          isComplete={materialProgressFinished}
          isDisabled={isDisabled}
        />
      </QualityDetailsListGroup>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('evidence.title')}
        progressPercentagesDescription={progressPopoverMessages('evidence.titleInfo', {
          complete: evidenceProgress.completed,
          total: evidenceProgress.total,
        })}
        isComplete={evidenceFinished}
        isDisabled={isDisabled}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('evidence.reportsEvidenced', {
            complete: evidenceItems.completed,
            total: evidenceItems.total,
          })}
          isDisabled={isDisabled}
          isComplete={evidenceFinished}
        />
        <QualityDetailsListProgressBar
          currentProgress={evidenceItemsPercentage}
          isComplete={evidenceFinished}
          isDisabled={isDisabled}
        />
      </QualityDetailsListGroup>

      <Divider orientation="horizontal" />
      <QualityDetailsListGroup
        title={progressPopoverMessages('allocations.title')}
        progressPercentagesDescription={progressPopoverMessages('allocations.titleInfo', {
          complete: allocationsProgress.completed,
          total: allocationsProgress.total,
        })}
        isComplete={allocationsProgressFinished}
        isDisabled={isDisabled}
      >
        <QualityDetailsListText
          text={progressPopoverMessages('allocations.resourcesAllocated', {
            complete: allocationsItems.completed,
            total: allocationsItems.total,
          })}
          isDisabled={isDisabled}
          isComplete={allocationsProgressFinished}
        />
        <QualityDetailsListProgressBar
          currentProgress={allocationsItemsPercentage}
          isComplete={allocationsProgressFinished}
          isDisabled={isDisabled}
        />
      </QualityDetailsListGroup>
    </div>
  );
};
