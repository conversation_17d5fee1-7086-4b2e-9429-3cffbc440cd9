import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { CursorPaginationMetaSchema } from '@shape-construction/api/src/types';
import Pagination from '@shape-construction/arch-ui/src/Pagination';
import Table from '@shape-construction/arch-ui/src/Table';
import { parseReactElement } from '@shape-construction/utils/dom';
import type { PaginationCursor } from 'app/hooks/usePagination';

interface TablePaginationProps {
  count: number;
  meta: CursorPaginationMetaSchema;
  onNext: (cursor: PaginationCursor) => void;
  onPrevious: (cursor: PaginationCursor) => void;
}

export const TablePagination = ({ count, meta, onNext, onPrevious }: TablePaginationProps) => {
  const paginationMessages = useMessageGetter('table.cursorPagination');

  return (
    <tfoot>
      <Table.Row>
        <Table.Cell colSpan={100}>
          <Pagination
            {...meta}
            onNext={onNext}
            onPrevious={onPrevious}
            previousButtonLabel={paginationMessages('previous')}
            nextButtonLabel={paginationMessages('next')}
            resultsCountLabel={parseReactElement(paginationMessages('resultsCount', { count, total: meta.total }))}
          />
        </Table.Cell>
      </Table.Row>
    </tfoot>
  );
};
