import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { QuestionMarkCircleIcon as QuestionMarkCircleIconOutline } from '@shape-construction/arch-ui/src/Icons/outline';
import { QuestionMarkCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useModal } from '@shape-construction/hooks';
import { parseReactElement } from '@shape-construction/utils/dom';

export const ProgressHint = () => {
  const messages = useMessageGetter('shiftReport.form.progressHint');
  const { open, openModal, closeModal } = useModal(false);

  return (
    <>
      <IconButton
        variant="text"
        size="xxs"
        color="secondary"
        icon={QuestionMarkCircleIcon}
        onClick={openModal}
        aria-label="progress-hint"
      />
      <ConfirmationModal.Root open={open} onClose={closeModal}>
        <ConfirmationModal.Header>
          <ConfirmationModal.Image>
            <IconBadge type="info">
              <QuestionMarkCircleIconOutline />
            </IconBadge>
          </ConfirmationModal.Image>
          <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
          <ConfirmationModal.SubTitle>{parseReactElement(messages('subtitle'))}</ConfirmationModal.SubTitle>
        </ConfirmationModal.Header>
        <ConfirmationModal.Footer>
          <Button color="primary" variant="contained" size="md" onClick={closeModal}>
            {messages('okCTA')}
          </Button>
        </ConfirmationModal.Footer>
      </ConfirmationModal.Root>
    </>
  );
};
