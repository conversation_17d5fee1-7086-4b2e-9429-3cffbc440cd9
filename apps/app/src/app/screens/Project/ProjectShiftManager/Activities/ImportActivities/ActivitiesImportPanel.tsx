import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { dismissToast, showInfoToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { parseReactElement } from '@shape-construction/utils/dom';
import { useShiftActivitiesImport } from 'app/queries/activities/activities';
import { useParams } from 'react-router';
import type { ImportPickerOptions } from '../../ShiftReports/components/ShiftReportImport/components/ShiftReportImportPicker';
import { ActivitiesImportPicker } from './ActivitiesImportPicker';
import { useRefetchActivities } from './hooks';

type Params = {
  projectId: ProjectSchema['id'];
};

export const ActivitiesImportPanel = () => {
  const messages = useMessageGetter('activities.import.options.importData.toast');
  const { projectId } = useParams<Params>() as Params;
  const { mutate: importShiftActivities } = useShiftActivitiesImport();
  const [, setRefetchActivities] = useRefetchActivities();

  const uploadXlsxTemplate = ([file]: File[]) => {
    if (!file) return;

    showInfoToast({
      message: messages('checkingErrors'),
      alignContent: 'start',
      hasIcon: false,
      id: 'import-activity-checking-errors',
    });

    importShiftActivities(
      {
        projectId,
        data: {
          xlsx: file,
        },
      },
      {
        onSuccess: () => {
          setRefetchActivities(true);
          dismissToast('import-activity-checking-errors');

          showInfoToast({
            message: parseReactElement(messages('importState')),
            alignContent: 'start',
            hasIcon: false,
            id: 'import-activity-state',
            duration: Number.POSITIVE_INFINITY,
          });
        },
        onError: () => {
          dismissToast('import-activity-checking-errors');
        },
      }
    );
  };

  const options: ImportPickerOptions = {
    downloadTemplate: {
      enabled: true,
      target: '/documents/shape-import-activities-template.xlsx',
    },
    importData: {
      accept: '.xlsx',
      enabled: true,
      multiple: false,
      onSelectFiles: uploadXlsxTemplate,
    },
  };

  return (
    <div className="mr-2">
      <ActivitiesImportPicker options={options} />
    </div>
  );
};
