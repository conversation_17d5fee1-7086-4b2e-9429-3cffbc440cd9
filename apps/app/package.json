{"name": "app", "version": "0.1.1", "private": true, "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"start": "vite", "start:pwa": "ENABLE_SERVICE_WORKER=true vite", "preview": "vite preview", "build": "vite build", "build:serve": "npm run build && npm run preview", "compile": "npm-run-all --parallel compile:**", "compile:app": "tsc -p tsconfig.json", "compile:app:integration": "tsc -p tsconfig.integration.json", "compile:cypress": "tsc -p ./cypress/tsconfig.json", "test": "jest --watch", "test:ci": "jest --ci --maxWorkers=100% --coverage --logHeapUsage --forceExit", "lint": "biome check .", "lint:autofix": "biome check --write .", "lint:translations": "node ./scripts/checkTranslations.js", "format": "biome format --write", "cy": "npx cypress open", "cy:run": "npx cypress run"}, "dependencies": {"@azure/msal-browser": "3.26.1", "@azure/msal-react": "2.1.1", "@babel/core": "7.28.0", "@headlessui/react": "1.7.15", "@hookform/resolvers": "2.9.10", "@hotjar/browser": "1.0.9", "@messageformat/core": "^3.0.0", "@messageformat/react": "^1.0.0", "@messageformat/runtime": "3.0.1", "@open-iframe-resizer/core": "1.6.0", "@rails/actioncable": "8.0.200", "@react-oauth/google": "0.12.2", "@reduxjs/toolkit": "^1.9.3", "@sentry/react": "8.38.0", "@shape-construction/api": "workspace:*", "@shape-construction/arch-ui": "workspace:*", "@shape-construction/feature-flags": "workspace:*", "@shape-construction/hooks": "workspace:*", "@shape-construction/react-image-markup": "workspace:*", "@shape-construction/utils": "workspace:*", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.10", "@tailwindcss/typography": "0.5.16", "@tanstack/react-query": "5.52.1", "@tanstack/react-query-devtools": "5.52.1", "@tanstack/react-query-persist-client": "5.52.1", "@testing-library/cypress": "10.0.2", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "14.6.1", "@tiptap/extension-hard-break": "^2.2.4", "@tiptap/extension-mention": "^2.2.4", "@tiptap/extension-placeholder": "^2.2.4", "@tiptap/pm": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@types/css-mediaquery": "^0.1.1", "@types/gtag.js": "0.0.12", "@types/history": "4.7.11", "@types/jest": "29.4.0", "@types/lodash.capitalize": "4.2.9", "@types/lodash.findlastindex": "4.6.9", "@types/lodash.flatten": "4.4.9", "@types/lodash.get": "4.4.9", "@types/lodash.groupby": "4.6.9", "@types/lodash.isequal": "4.5.8", "@types/lodash.omit": "4.5.9", "@types/lodash.orderby": "4.6.9", "@types/lodash.result": "4.5.9", "@types/lodash.snakecase": "4.1.9", "@types/lodash.sortby": "4.7.9", "@types/node": "^18.11.18", "@types/pg": "8.15.4", "@types/rails__actioncable": "6.1.11", "@types/react": "19.0.10", "@types/react-dom": "19.1.7", "@types/react-helmet": "^6.1.5", "@types/react-redux": "^7.1.25", "@types/redux-mock-store": "1.5.0", "@types/spark-md5": "3.0.4", "@types/uuid": "^8.3.4", "@vitejs/plugin-react": "4.3.3", "axios": "1.10.0", "axios-case-converter": "1.1.1", "babel-jest": "^29.5.0", "babel-preset-react-app": "10.1.0", "browserslist": "4.25.1", "camelcase": "^6.2.1", "chai-sorted": "^0.2.0", "change-case": "^5.4.4", "clipboard-polyfill": "^3.0.2", "core-js": "3.44.0", "css-mediaquery": "^0.1.2", "cssom": "0.5.0", "cypress": "14.5.1", "cypress-audit": "1.1.0", "cypress-browser-permissions": "1.1.0", "cypress-file-upload": "5.0.8", "dexie": "4.0.11", "dexie-react-hooks": "1.1.7", "dotenv": "17.2.0", "fake-indexeddb": "6.0.1", "formik": "^2.2.9", "history": "^4.10.1", "identity-obj-proxy": "^3.0.0", "immer": "10.1.1", "jest": "29.4.3", "jest-canvas-mock": "2.5.2", "jest-environment-jsdom": "29.7.0", "jest-localstorage-mock": "^2.4.26", "jest-watch-typeahead": "2.2.2", "jotai": "^1.6.4", "localforage": "^1.10.0", "lodash.capitalize": "4.2.1", "lodash.findlastindex": "4.6.0", "lodash.flatten": "4.4.0", "lodash.get": "4.4.2", "lodash.groupby": "4.6.0", "lodash.isequal": "4.5.0", "lodash.omit": "4.5.0", "lodash.orderby": "4.6.0", "lodash.result": "4.5.2", "lodash.snakecase": "4.1.1", "lodash.sortby": "4.7.0", "msw": "2.7.0", "npm-run-all": "4.1.5", "pg": "8.16.3", "postcss": "8.4.13", "prosemirror-state": "1.4.2", "query-string": "^6.12.1", "react": "19.0.0", "react-app-polyfill": "^3.0.0", "react-cookie": "8.0.1", "react-dom": "19.0.0", "react-dropzone": "14.3.8", "react-helmet-async": "2.0.5", "react-hook-form": "7.43.2", "react-image-lightbox": "^5.1.4", "react-redux": "^8.0.5", "react-router": "7.8.2", "react-smooth-dnd": "^0.11.1", "react-waypoint": "^10.3.0", "recharts": "^2.4.2", "redux": "^4.2.1", "redux-mock-store": "1.5.5", "redux-persist": "^6.0.0", "resize-observer-polyfill": "1.5.1", "rollup-plugin-messageformat": "3.0.0", "service-worker-mock": "^2.0.5", "spark-md5": "3.0.2", "stream-chat": "9.6.0", "stream-chat-react": "13.1.0", "superjson": "2.2.2", "tailwindcss": "4.1.10", "typescript": "5.8.3", "ua-parser-js": "^1.0.2", "uuid": "^9.0.0", "vite": "7.1.3", "vite-plugin-environment": "1.1.3", "vite-plugin-pwa": "1.0.3", "vite-plugin-svgr": "4.5.0", "vite-tsconfig-paths": "5.1.4", "workbox-cacheable-response": "7.3.0", "workbox-core": "7.3.0", "workbox-expiration": "7.3.0", "workbox-precaching": "7.3.0", "workbox-routing": "7.3.0", "workbox-strategies": "7.3.0", "workbox-window": "7.3.0", "yup": "^0.32.9"}, "browserslist": {"production": ">0.2%, not dead, not op_mini all", "development": "ie 11, last 1 chrome version, last 1 firefox version, last 1 safari version"}, "overrides": {"cypress": {"@types/node": "$@types/node"}}, "devDependencies": {"@biomejs/biome": "2.0.6", "@figma-export/cli": "5.0.1", "@figma-export/output-components-as-svgr": "5.0.1", "@kubb/plugin-client": "3.3.4", "@svgr/plugin-jsx": "8.1.0", "@svgr/plugin-svgo": "8.1.0", "@swc/core": "1.10.11", "@swc/jest": "0.2.37", "@types/ua-parser-js": "0.7.39", "jest-fixed-jsdom": "0.0.9", "js-yaml": "4.1.0", "swc_mut_cjs_exports": "8.0.1"}}