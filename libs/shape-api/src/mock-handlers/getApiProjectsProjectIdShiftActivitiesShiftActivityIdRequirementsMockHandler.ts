/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
