/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdSchema';
import { createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId';
import { http } from 'msw';

export function patchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
