/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssueViewsIssueViewIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssueViewsIssueViewIdSchema';
import { createDeleteApiProjectsProjectIdIssueViewsIssueViewIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssueViewsIssueViewId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssueViewsIssueViewIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssueViewsIssueViewIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/issue_views/:issue_view_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdIssueViewsIssueViewIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
