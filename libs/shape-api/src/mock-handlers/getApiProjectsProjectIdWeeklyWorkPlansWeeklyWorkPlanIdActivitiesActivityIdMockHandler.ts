/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/activities/:activity_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
