/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsSchema';
import { createGetApiProjectsProjectIdTeamsQueryResponse } from '../factories/createGetApiProjectsProjectIdTeams';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdTeamsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
