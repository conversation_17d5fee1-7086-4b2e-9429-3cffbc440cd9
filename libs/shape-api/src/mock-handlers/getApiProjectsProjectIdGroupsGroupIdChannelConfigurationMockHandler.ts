/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryResponseSchema } from '../types/getApiProjectsProjectIdGroupsGroupIdChannelConfigurationSchema';
import { createGetApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryResponse } from '../factories/createGetApiProjectsProjectIdGroupsGroupIdChannelConfiguration';
import { http } from 'msw';

export function getApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler(
  data?:
    | GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/groups/:group_id/channel_configuration', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
