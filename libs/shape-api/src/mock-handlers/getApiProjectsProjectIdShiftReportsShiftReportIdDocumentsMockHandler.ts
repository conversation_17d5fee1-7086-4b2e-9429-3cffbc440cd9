/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/:shift_report_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
