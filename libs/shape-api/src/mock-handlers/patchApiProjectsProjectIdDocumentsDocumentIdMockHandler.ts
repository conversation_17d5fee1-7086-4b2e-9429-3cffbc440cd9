/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdDocumentsDocumentIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdDocumentsDocumentIdSchema';
import { createPatchApiProjectsProjectIdDocumentsDocumentIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdDocumentsDocumentId';
import { http } from 'msw';

export function patchApiProjectsProjectIdDocumentsDocumentIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdDocumentsDocumentIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/documents/:document_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdDocumentsDocumentIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
