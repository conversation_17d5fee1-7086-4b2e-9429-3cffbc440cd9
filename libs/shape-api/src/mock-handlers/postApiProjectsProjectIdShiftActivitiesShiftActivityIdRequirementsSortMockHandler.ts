/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements/sort',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
