/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdSubmitMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdSubmitSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdSubmitMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdSubmit';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdSubmitMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdSubmitMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/submit', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdSubmitMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
