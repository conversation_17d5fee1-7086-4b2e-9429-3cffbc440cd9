/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationResponseSchema } from '../types/putApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdSchema';
import { createPutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationResponse } from '../factories/createPutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId';
import { http } from 'msw';

export function putApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMockHandler(
  data?:
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.put>[1]>[0]) => Response)
) {
  return http.put('*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
