/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdIssueImagesSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdIssueImages';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdIssueImagesMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/issue_images', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
