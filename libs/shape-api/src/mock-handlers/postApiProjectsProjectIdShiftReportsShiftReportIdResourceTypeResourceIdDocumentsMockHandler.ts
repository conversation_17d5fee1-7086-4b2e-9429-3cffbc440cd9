/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/:resource_type/:resource_id/documents',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationResponse(data)
        ),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
