/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMutationResponseSchema } from '../types/postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectSchema';
import { createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMutationResponse } from '../factories/createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject';
import { http } from 'msw';

export function postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMockHandler(
  data?:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/access_requests/:project_access_request_id/reject',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
