/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationResponseSchema } from '../types/postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteSchema';
import { createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationResponse } from '../factories/createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete';
import { http } from 'msw';

export function postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler(
  data?:
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id/change_signal_links/batch_delete',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationResponse(
              data
            )
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
