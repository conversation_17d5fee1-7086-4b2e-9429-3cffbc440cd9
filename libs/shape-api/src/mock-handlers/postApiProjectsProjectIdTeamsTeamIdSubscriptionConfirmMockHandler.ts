/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/subscription/confirm', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
