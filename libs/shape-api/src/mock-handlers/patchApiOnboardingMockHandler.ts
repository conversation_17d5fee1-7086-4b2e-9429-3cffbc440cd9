/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiOnboardingMutationResponseSchema } from '../types/patchApiOnboardingSchema';
import { createPatchApiOnboardingMutationResponse } from '../factories/createPatchApiOnboarding';
import { http } from 'msw';

export function patchApiOnboardingMockHandler(
  data?:
    | PatchApiOnboardingMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/onboarding', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiOnboardingMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
