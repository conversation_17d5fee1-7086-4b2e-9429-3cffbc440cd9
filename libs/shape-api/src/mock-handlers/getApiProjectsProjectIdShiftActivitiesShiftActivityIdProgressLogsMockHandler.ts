/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
