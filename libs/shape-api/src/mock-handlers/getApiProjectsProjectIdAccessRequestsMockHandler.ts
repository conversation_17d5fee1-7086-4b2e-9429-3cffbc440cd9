/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdAccessRequestsQueryResponseSchema } from '../types/getApiProjectsProjectIdAccessRequestsSchema';
import { createGetApiProjectsProjectIdAccessRequestsQueryResponse } from '../factories/createGetApiProjectsProjectIdAccessRequests';
import { http } from 'msw';

export function getApiProjectsProjectIdAccessRequestsMockHandler(
  data?:
    | GetApiProjectsProjectIdAccessRequestsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/access_requests', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdAccessRequestsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
