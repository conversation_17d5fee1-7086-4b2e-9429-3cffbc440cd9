/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_activities/:shift_activity_id/archive', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
