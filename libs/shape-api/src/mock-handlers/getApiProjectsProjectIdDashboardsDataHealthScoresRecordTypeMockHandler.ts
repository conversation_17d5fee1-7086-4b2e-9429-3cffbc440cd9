/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryResponseSchema } from '../types/getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeSchema';
import { createGetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryResponse } from '../factories/createGetApiProjectsProjectIdDashboardsDataHealthScoresRecordType';
import { http } from 'msw';

export function getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(
  data?:
    | GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/dashboards/data_health_scores/:record_type', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
