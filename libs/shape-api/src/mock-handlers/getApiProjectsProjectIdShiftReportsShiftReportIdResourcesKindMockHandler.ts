/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/:shift_report_id/resources/:kind', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
