/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiUsersConfirmationInstructionsMutationResponseSchema } from '../types/postApiUsersConfirmationInstructionsSchema';
import { createPostApiUsersConfirmationInstructionsMutationResponse } from '../factories/createPostApiUsersConfirmationInstructions';
import { http } from 'msw';

export function postApiUsersConfirmationInstructionsMockHandler(
  data?:
    | PostApiUsersConfirmationInstructionsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/users/confirmation/instructions', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiUsersConfirmationInstructionsMutationResponse(data)), {
      status: 204,
    });
  });
}
