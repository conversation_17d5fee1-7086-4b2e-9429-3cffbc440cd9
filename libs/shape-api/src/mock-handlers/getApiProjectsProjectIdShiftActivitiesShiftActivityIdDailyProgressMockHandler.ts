/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/daily_progress',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
