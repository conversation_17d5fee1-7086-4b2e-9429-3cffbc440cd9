/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiOrgsQueryResponseSchema } from '../types/getApiOrgsSchema';
import { createGetApiOrgsQueryResponse } from '../factories/createGetApiOrgs';
import { http } from 'msw';

export function getApiOrgsMockHandler(
  data?: GetApiOrgsQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/orgs', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiOrgsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
