/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProductToursProductTourKeyMutationResponseSchema } from '../types/patchApiProductToursProductTourKeySchema';
import { createPatchApiProductToursProductTourKeyMutationResponse } from '../factories/createPatchApiProductToursProductTourKey';
import { http } from 'msw';

export function patchApiProductToursProductTourKeyMockHandler(
  data?:
    | PatchApiProductToursProductTourKeyMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/product_tours/:product_tour_key', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiProductToursProductTourKeyMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
