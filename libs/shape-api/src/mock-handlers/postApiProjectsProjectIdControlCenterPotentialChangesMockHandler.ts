/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema } from '../types/postApiProjectsProjectIdControlCenterPotentialChangesSchema';
import { createPostApiProjectsProjectIdControlCenterPotentialChangesMutationResponse } from '../factories/createPostApiProjectsProjectIdControlCenterPotentialChanges';
import { http } from 'msw';

export function postApiProjectsProjectIdControlCenterPotentialChangesMockHandler(
  data?:
    | PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/control_center/potential_changes', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdControlCenterPotentialChangesMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
