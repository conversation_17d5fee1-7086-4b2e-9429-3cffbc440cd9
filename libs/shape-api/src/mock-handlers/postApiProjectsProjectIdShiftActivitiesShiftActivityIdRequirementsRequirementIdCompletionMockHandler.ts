/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements/:requirement_id/completion',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponse(
              data
            )
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
