/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiPushSubscriptionsMutationResponseSchema } from '../types/postApiPushSubscriptionsSchema';
import { createPostApiPushSubscriptionsMutationResponse } from '../factories/createPostApiPushSubscriptions';
import { http } from 'msw';

export function postApiPushSubscriptionsMockHandler(
  data?:
    | PostApiPushSubscriptionsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/push_subscriptions', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiPushSubscriptionsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
