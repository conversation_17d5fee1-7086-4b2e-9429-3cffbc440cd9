/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdSchema';
import { createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId';
import { http } from 'msw';

export function patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs/:progress_log_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
