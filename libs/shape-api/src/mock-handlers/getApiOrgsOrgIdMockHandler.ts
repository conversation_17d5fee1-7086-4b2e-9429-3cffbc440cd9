/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiOrgsOrgIdQueryResponseSchema } from '../types/getApiOrgsOrgIdSchema';
import { createGetApiOrgsOrgIdQueryResponse } from '../factories/createGetApiOrgsOrgId';
import { http } from 'msw';

export function getApiOrgsOrgIdMockHandler(
  data?: GetApiOrgsOrgIdQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/orgs/:org_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiOrgsOrgIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
