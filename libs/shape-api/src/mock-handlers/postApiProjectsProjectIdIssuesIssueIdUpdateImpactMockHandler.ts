/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdUpdateImpactSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdUpdateImpact';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdUpdateImpactMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/update_impact', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
