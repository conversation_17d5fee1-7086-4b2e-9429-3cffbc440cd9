/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationResponse(data)
        ),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
