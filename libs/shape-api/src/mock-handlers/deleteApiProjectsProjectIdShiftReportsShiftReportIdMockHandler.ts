/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftReportsShiftReportIdSchema';
import { createDeleteApiProjectsProjectIdShiftReportsShiftReportIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftReportsShiftReportId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/shift_reports/:shift_report_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdShiftReportsShiftReportIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
