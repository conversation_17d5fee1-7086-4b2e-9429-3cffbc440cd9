/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdCustomFieldsCustomFieldIdSchema';
import { createPatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdCustomFieldsCustomFieldId';
import { http } from 'msw';

export function patchApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/custom_fields/:custom_field_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
