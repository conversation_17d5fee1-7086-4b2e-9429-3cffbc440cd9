/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/issues/:issue_id/status_statements/:status_statement_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createDeleteApiProjectsProjectIdIssuesIssueIdStatusStatementsStatusStatementIdMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
