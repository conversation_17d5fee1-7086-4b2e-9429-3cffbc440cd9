/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdSchema';
import { createDeleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/teams/:team_id/members/:team_member_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
