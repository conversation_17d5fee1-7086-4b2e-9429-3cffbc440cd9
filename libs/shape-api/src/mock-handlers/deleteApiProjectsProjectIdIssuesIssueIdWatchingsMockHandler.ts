/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdWatchingsSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueIdWatchings';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdWatchingsMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/issues/:issue_id/watchings', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
