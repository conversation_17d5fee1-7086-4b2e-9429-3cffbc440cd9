/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationResponseSchema } from '../types/patchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderSchema';
import { createPatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationResponse } from '../factories/createPatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder';
import { http } from 'msw';

export function patchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler(
  data?:
    | PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/weekly_work_plans/shift_activities_finder', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
