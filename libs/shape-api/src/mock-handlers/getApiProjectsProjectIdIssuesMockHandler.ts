/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesSchema';
import { createGetApiProjectsProjectIdIssuesQueryResponse } from '../factories/createGetApiProjectsProjectIdIssues';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssuesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
