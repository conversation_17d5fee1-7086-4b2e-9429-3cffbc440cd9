/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiAgreementsLatestEuaQueryResponseSchema } from '../types/getApiAgreementsLatestEuaSchema';
import { createGetApiAgreementsLatestEuaQueryResponse } from '../factories/createGetApiAgreementsLatestEua';
import { http } from 'msw';

export function getApiAgreementsLatestEuaMockHandler(
  data?:
    | GetApiAgreementsLatestEuaQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/agreements/latest_eua', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiAgreementsLatestEuaQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
