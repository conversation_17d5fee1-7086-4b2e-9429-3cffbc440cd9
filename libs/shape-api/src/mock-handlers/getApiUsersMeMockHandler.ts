/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiUsersMeQueryResponseSchema } from '../types/getApiUsersMeSchema';
import { createGetApiUsersMeQueryResponse } from '../factories/createGetApiUsersMe';
import { http } from 'msw';

export function getApiUsersMeMockHandler(
  data?: GetApiUsersMeQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/users/me', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiUsersMeQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
