/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiPushSubscriptionsPushSubscriptionIdMutationResponseSchema } from '../types/patchApiPushSubscriptionsPushSubscriptionIdSchema';
import { createPatchApiPushSubscriptionsPushSubscriptionIdMutationResponse } from '../factories/createPatchApiPushSubscriptionsPushSubscriptionId';
import { http } from 'msw';

export function patchApiPushSubscriptionsPushSubscriptionIdMockHandler(
  data?:
    | PatchApiPushSubscriptionsPushSubscriptionIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/push_subscriptions/:push_subscription_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiPushSubscriptionsPushSubscriptionIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
