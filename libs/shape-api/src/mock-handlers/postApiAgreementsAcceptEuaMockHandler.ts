/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiAgreementsAcceptEuaMutationResponseSchema } from '../types/postApiAgreementsAcceptEuaSchema';
import { createPostApiAgreementsAcceptEuaMutationResponse } from '../factories/createPostApiAgreementsAcceptEua';
import { http } from 'msw';

export function postApiAgreementsAcceptEuaMockHandler(
  data?:
    | PostApiAgreementsAcceptEuaMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/agreements/accept_eua', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiAgreementsAcceptEuaMutationResponse(data)), {
      status: 204,
    });
  });
}
