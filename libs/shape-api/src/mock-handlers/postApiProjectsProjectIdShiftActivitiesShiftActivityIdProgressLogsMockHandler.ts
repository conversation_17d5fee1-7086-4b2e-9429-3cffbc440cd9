/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationResponse(data)
        ),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
