/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/restore', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMutationResponse(data)
      ),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
