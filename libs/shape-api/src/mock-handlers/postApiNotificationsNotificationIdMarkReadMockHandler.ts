/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiNotificationsNotificationIdMarkReadMutationResponseSchema } from '../types/postApiNotificationsNotificationIdMarkReadSchema';
import { createPostApiNotificationsNotificationIdMarkReadMutationResponse } from '../factories/createPostApiNotificationsNotificationIdMarkRead';
import { http } from 'msw';

export function postApiNotificationsNotificationIdMarkReadMockHandler(
  data?:
    | PostApiNotificationsNotificationIdMarkReadMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/notifications/:notification_id/mark_read', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiNotificationsNotificationIdMarkReadMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
