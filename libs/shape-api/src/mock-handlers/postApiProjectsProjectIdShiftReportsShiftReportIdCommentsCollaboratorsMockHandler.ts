/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/comments/collaborators',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationResponse(data)
        ),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
