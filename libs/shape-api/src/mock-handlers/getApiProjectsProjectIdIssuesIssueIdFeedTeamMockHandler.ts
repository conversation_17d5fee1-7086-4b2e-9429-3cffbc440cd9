/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdFeedTeamSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdFeedTeam';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdFeedTeamMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/feed/team', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
