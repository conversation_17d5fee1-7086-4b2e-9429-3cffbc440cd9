/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/issues/:issue_id/assignments/:issue_assignment_id/reject',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
