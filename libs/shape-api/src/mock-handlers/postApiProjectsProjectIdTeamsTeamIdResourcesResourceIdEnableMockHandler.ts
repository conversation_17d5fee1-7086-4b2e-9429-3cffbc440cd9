/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/resources/:resource_id/enable', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
