/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueId';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
