/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/teams/:team_id/members/:team_member_id/resend_invite_email',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMutationResponse(data)
        ),
        {
          status: 202,
        }
      );
    }
  );
}
