/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdDocumentsSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdDocuments';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
