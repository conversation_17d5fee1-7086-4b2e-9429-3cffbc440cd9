/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiNotificationsOverviewQueryResponseSchema } from '../types/getApiNotificationsOverviewSchema';
import { createGetApiNotificationsOverviewQueryResponse } from '../factories/createGetApiNotificationsOverview';
import { http } from 'msw';

export function getApiNotificationsOverviewMockHandler(
  data?:
    | GetApiNotificationsOverviewQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/notifications/overview', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiNotificationsOverviewQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
