/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiUsersMutationResponseSchema } from '../types/postApiUsersSchema';
import { createPostApiUsersMutationResponse } from '../factories/createPostApiUsers';
import { http } from 'msw';

export function postApiUsersMockHandler(
  data?: PostApiUsersMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/users', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiUsersMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
