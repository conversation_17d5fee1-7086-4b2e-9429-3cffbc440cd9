/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/progress_logs',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
