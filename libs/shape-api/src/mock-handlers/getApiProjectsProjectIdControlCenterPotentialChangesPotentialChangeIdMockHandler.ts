/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryResponseSchema } from '../types/getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdSchema';
import { createGetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryResponse } from '../factories/createGetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId';
import { http } from 'msw';

export function getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMockHandler(
  data?:
    | GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
