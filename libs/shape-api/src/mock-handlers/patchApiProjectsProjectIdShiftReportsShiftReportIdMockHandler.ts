/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdShiftReportsShiftReportIdSchema';
import { createPatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdShiftReportsShiftReportId';
import { http } from 'msw';

export function patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/shift_reports/:shift_report_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
