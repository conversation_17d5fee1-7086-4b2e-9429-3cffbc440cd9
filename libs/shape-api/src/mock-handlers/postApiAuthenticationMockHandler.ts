/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiAuthenticationMutationResponseSchema } from '../types/postApiAuthenticationSchema';
import { createPostApiAuthenticationMutationResponse } from '../factories/createPostApiAuthentication';
import { http } from 'msw';

export function postApiAuthenticationMockHandler(
  data?:
    | PostApiAuthenticationMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/authentication', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiAuthenticationMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
