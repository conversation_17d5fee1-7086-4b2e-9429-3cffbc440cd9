/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryResponseSchema } from '../types/getApiProjectsProjectIdControlCenterChangeSignalsIssuesSchema';
import { createGetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryResponse } from '../factories/createGetApiProjectsProjectIdControlCenterChangeSignalsIssues';
import { http } from 'msw';

export function getApiProjectsProjectIdControlCenterChangeSignalsIssuesMockHandler(
  data?:
    | GetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/control_center/change_signals/issues', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
