/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiOnboardingFinishMutationResponseSchema } from '../types/postApiOnboardingFinishSchema';
import { createPostApiOnboardingFinishMutationResponse } from '../factories/createPostApiOnboardingFinish';
import { http } from 'msw';

export function postApiOnboardingFinishMockHandler(
  data?:
    | PostApiOnboardingFinishMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/onboarding/finish', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiOnboardingFinishMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
