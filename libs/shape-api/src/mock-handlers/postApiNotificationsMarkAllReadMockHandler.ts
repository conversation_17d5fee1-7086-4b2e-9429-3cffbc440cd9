/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiNotificationsMarkAllReadMutationResponseSchema } from '../types/postApiNotificationsMarkAllReadSchema';
import { createPostApiNotificationsMarkAllReadMutationResponse } from '../factories/createPostApiNotificationsMarkAllRead';
import { http } from 'msw';

export function postApiNotificationsMarkAllReadMockHandler(
  data?:
    | PostApiNotificationsMarkAllReadMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/notifications/mark_all_read', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiNotificationsMarkAllReadMutationResponse(data)), {
      status: 204,
    });
  });
}
