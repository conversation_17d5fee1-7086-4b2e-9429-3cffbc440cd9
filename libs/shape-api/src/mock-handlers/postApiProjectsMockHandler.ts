/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsMutationResponseSchema } from '../types/postApiProjectsSchema';
import { createPostApiProjectsMutationResponse } from '../factories/createPostApiProjects';
import { http } from 'msw';

export function postApiProjectsMockHandler(
  data?: PostApiProjectsMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
