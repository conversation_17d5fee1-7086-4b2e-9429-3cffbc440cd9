/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdSchema';
import { createPatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId';
import { http } from 'msw';

export function patchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/issues/:issue_id/issue_images/:issue_image_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
