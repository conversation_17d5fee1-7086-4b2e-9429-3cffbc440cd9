/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdResourcesKindSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamIdResourcesKind';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams/:team_id/resources/:kind', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
