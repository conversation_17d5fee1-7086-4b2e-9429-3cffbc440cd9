/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiOrgsMutationResponseSchema } from '../types/postApiOrgsSchema';
import { createPostApiOrgsMutationResponse } from '../factories/createPostApiOrgs';
import { http } from 'msw';

export function postApiOrgsMockHandler(
  data?: PostApiOrgsMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/orgs', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiOrgsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
