/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiOrgsOrgIdResendVerificationEmailMutationResponseSchema } from '../types/postApiOrgsOrgIdResendVerificationEmailSchema';
import { createPostApiOrgsOrgIdResendVerificationEmailMutationResponse } from '../factories/createPostApiOrgsOrgIdResendVerificationEmail';
import { http } from 'msw';

export function postApiOrgsOrgIdResendVerificationEmailMockHandler(
  data?:
    | PostApiOrgsOrgIdResendVerificationEmailMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/orgs/:org_id/resend_verification_email', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiOrgsOrgIdResendVerificationEmailMutationResponse(data)), {
      status: 204,
    });
  });
}
