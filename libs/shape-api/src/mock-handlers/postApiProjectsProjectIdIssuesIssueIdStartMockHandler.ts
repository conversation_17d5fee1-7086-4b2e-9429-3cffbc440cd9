/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdStartMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdStartSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdStartMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdStart';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdStartMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdStartMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/start', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdStartMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
