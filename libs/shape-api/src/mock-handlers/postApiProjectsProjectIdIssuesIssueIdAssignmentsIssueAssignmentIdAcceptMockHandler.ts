/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/issues/:issue_id/assignments/:issue_assignment_id/accept',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
