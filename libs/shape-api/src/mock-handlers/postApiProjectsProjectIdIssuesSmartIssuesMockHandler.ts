/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesSmartIssuesMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesSmartIssuesSchema';
import { createPostApiProjectsProjectIdIssuesSmartIssuesMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesSmartIssues';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesSmartIssuesMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesSmartIssuesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/smart_issues', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdIssuesSmartIssuesMutationResponse(data)), {
      status: 202,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
