/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdCustomFieldsQueryResponseSchema } from '../types/getApiProjectsProjectIdCustomFieldsSchema';
import { createGetApiProjectsProjectIdCustomFieldsQueryResponse } from '../factories/createGetApiProjectsProjectIdCustomFields';
import { http } from 'msw';

export function getApiProjectsProjectIdCustomFieldsMockHandler(
  data?:
    | GetApiProjectsProjectIdCustomFieldsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/custom_fields', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdCustomFieldsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
