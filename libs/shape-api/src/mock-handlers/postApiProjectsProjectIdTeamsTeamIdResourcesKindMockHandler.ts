/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdResourcesKindSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdResourcesKind';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/resources/:kind', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
