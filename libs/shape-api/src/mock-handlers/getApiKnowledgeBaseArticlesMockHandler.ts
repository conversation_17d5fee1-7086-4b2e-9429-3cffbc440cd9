/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiKnowledgeBaseArticlesQueryResponseSchema } from '../types/getApiKnowledgeBaseArticlesSchema';
import { createGetApiKnowledgeBaseArticlesQueryResponse } from '../factories/createGetApiKnowledgeBaseArticles';
import { http } from 'msw';

export function getApiKnowledgeBaseArticlesMockHandler(
  data?:
    | GetApiKnowledgeBaseArticlesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/knowledge_base/articles', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiKnowledgeBaseArticlesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
