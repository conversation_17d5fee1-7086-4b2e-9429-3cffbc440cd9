/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/resources/:resource_id/disable', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
