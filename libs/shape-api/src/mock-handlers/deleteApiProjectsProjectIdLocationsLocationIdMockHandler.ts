/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdLocationsLocationIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdLocationsLocationIdSchema';
import { createDeleteApiProjectsProjectIdLocationsLocationIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdLocationsLocationId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdLocationsLocationIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdLocationsLocationIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/locations/:location_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdLocationsLocationIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
