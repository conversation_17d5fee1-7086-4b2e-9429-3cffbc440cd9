/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdStopMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdStopSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdStopMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdStop';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdStopMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdStopMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/stop', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdStopMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
