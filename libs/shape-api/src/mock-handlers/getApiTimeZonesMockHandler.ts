/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiTimeZonesQueryResponseSchema } from '../types/getApiTimeZonesSchema';
import { createGetApiTimeZonesQueryResponse } from '../factories/createGetApiTimeZones';
import { http } from 'msw';

export function getApiTimeZonesMockHandler(
  data?: GetApiTimeZonesQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/time_zones', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiTimeZonesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
