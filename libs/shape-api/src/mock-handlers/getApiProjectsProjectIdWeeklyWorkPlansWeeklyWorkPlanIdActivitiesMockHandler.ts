/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/activities',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
