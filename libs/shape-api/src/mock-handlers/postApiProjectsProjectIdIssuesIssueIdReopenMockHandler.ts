/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdReopenMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdReopenSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdReopenMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdReopen';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdReopenMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdReopenMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/reopen', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdReopenMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
