/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/weekly_work_plans/shift_activities_finder', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
