/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssueViewsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssueViewsSchema';
import { createPostApiProjectsProjectIdIssueViewsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssueViews';
import { http } from 'msw';

export function postApiProjectsProjectIdIssueViewsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssueViewsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issue_views', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdIssueViewsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
