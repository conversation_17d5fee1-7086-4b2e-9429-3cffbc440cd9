/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationResponseSchema } from '../types/patchApiProjectsProjectIdTeamsTeamIdChannelConfigurationSchema';
import { createPatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationResponse } from '../factories/createPatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration';
import { http } from 'msw';

export function patchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler(
  data?:
    | PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/teams/:team_id/channel_configuration', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
