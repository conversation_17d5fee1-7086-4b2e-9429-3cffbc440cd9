/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsArchivedQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsArchivedSchema';
import { createGetApiProjectsProjectIdShiftReportsArchivedQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsArchived';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsArchivedMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsArchivedQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/archived', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsArchivedQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
