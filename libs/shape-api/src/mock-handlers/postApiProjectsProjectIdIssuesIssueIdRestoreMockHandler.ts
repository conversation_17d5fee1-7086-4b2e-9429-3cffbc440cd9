/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdRestoreMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdRestoreSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdRestoreMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdRestore';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdRestoreMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdRestoreMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/restore', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdRestoreMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
