/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdLocationsMutationResponseSchema } from '../types/postApiProjectsProjectIdLocationsSchema';
import { createPostApiProjectsProjectIdLocationsMutationResponse } from '../factories/createPostApiProjectsProjectIdLocations';
import { http } from 'msw';

export function postApiProjectsProjectIdLocationsMockHandler(
  data?:
    | PostApiProjectsProjectIdLocationsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/locations', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdLocationsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
