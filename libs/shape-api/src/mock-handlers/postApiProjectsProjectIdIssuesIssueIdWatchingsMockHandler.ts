/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdWatchingsSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdWatchings';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdWatchingsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/watchings', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
