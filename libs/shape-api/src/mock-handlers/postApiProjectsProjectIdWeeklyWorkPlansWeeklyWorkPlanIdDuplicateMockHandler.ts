/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/duplicate',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationResponse(data)
        ),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
