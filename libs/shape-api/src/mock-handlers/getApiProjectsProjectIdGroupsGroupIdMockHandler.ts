/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdGroupsGroupIdQueryResponseSchema } from '../types/getApiProjectsProjectIdGroupsGroupIdSchema';
import { createGetApiProjectsProjectIdGroupsGroupIdQueryResponse } from '../factories/createGetApiProjectsProjectIdGroupsGroupId';
import { http } from 'msw';

export function getApiProjectsProjectIdGroupsGroupIdMockHandler(
  data?:
    | GetApiProjectsProjectIdGroupsGroupIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/groups/:group_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdGroupsGroupIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
