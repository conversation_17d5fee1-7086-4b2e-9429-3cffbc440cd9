/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdAssignmentsSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdAssignments';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdAssignmentsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/assignments', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
