/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationResponseSchema } from '../types/postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptSchema';
import { createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationResponse } from '../factories/createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept';
import { http } from 'msw';

export function postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMockHandler(
  data?:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/access_requests/:project_access_request_id/accept',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
