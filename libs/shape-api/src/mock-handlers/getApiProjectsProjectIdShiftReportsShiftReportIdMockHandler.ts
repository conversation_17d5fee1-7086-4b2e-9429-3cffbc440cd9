/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportId';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/:shift_report_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsShiftReportIdQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
