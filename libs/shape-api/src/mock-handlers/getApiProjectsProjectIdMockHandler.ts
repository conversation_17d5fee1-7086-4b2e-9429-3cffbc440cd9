/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdQueryResponseSchema } from '../types/getApiProjectsProjectIdSchema';
import { createGetApiProjectsProjectIdQueryResponse } from '../factories/createGetApiProjectsProjectId';
import { http } from 'msw';

export function getApiProjectsProjectIdMockHandler(
  data?:
    | GetApiProjectsProjectIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
