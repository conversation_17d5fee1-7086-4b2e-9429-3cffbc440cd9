/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdControlCenterPotentialChangesQueryResponseSchema } from '../types/getApiProjectsProjectIdControlCenterPotentialChangesSchema';
import { createGetApiProjectsProjectIdControlCenterPotentialChangesQueryResponse } from '../factories/createGetApiProjectsProjectIdControlCenterPotentialChanges';
import { http } from 'msw';

export function getApiProjectsProjectIdControlCenterPotentialChangesMockHandler(
  data?:
    | GetApiProjectsProjectIdControlCenterPotentialChangesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/control_center/potential_changes', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdControlCenterPotentialChangesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
