/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdExportMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdExportSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdExportMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdExport';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdExportMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdExportMutationResponse(data)),
      {
        status: 202,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
