/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdEventsQueryResponseSchema } from '../types/getApiProjectsProjectIdEventsSchema';
import { createGetApiProjectsProjectIdEventsQueryResponse } from '../factories/createGetApiProjectsProjectIdEvents';
import { http } from 'msw';

export function getApiProjectsProjectIdEventsMockHandler(
  data?:
    | GetApiProjectsProjectIdEventsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/events', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdEventsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
