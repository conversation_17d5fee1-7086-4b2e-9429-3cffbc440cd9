/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/issues/:issue_id/issue_images/:issue_image_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createDeleteApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
