/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDisciplinesQueryResponseSchema } from '../types/getApiProjectsProjectIdDisciplinesSchema';
import { createGetApiProjectsProjectIdDisciplinesQueryResponse } from '../factories/createGetApiProjectsProjectIdDisciplines';
import { http } from 'msw';

export function getApiProjectsProjectIdDisciplinesMockHandler(
  data?:
    | GetApiProjectsProjectIdDisciplinesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/disciplines', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdDisciplinesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
