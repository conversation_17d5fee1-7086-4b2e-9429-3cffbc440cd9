/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDocumentsDocumentIdReferencesQueryResponseSchema } from '../types/getApiProjectsProjectIdDocumentsDocumentIdReferencesSchema';
import { createGetApiProjectsProjectIdDocumentsDocumentIdReferencesQueryResponse } from '../factories/createGetApiProjectsProjectIdDocumentsDocumentIdReferences';
import { http } from 'msw';

export function getApiProjectsProjectIdDocumentsDocumentIdReferencesMockHandler(
  data?:
    | GetApiProjectsProjectIdDocumentsDocumentIdReferencesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/documents/:document_id/references', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDocumentsDocumentIdReferencesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
