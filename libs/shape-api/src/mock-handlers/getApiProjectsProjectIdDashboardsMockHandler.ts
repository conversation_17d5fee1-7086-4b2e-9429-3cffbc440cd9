/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDashboardsQueryResponseSchema } from '../types/getApiProjectsProjectIdDashboardsSchema';
import { createGetApiProjectsProjectIdDashboardsQueryResponse } from '../factories/createGetApiProjectsProjectIdDashboards';
import { http } from 'msw';

export function getApiProjectsProjectIdDashboardsMockHandler(
  data?:
    | GetApiProjectsProjectIdDashboardsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/dashboards', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdDashboardsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
