/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdStatusStatementsSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdStatusStatements';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdStatusStatementsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/status_statements', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
