/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdGroupsMutationResponseSchema } from '../types/postApiProjectsProjectIdGroupsSchema';
import { createPostApiProjectsProjectIdGroupsMutationResponse } from '../factories/createPostApiProjectsProjectIdGroups';
import { http } from 'msw';

export function postApiProjectsProjectIdGroupsMockHandler(
  data?:
    | PostApiProjectsProjectIdGroupsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/groups', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdGroupsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
