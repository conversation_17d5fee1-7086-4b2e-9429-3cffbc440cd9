/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsSchema';
import { createPostApiProjectsProjectIdTeamsMutationResponse } from '../factories/createPostApiProjectsProjectIdTeams';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdTeamsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
