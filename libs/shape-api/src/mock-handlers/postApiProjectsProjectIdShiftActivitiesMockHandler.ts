/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesSchema';
import { createPostApiProjectsProjectIdShiftActivitiesMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivities';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_activities', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdShiftActivitiesMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
