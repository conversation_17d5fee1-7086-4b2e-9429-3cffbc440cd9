/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsCompletionsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsCompletionsSchema';
import { createGetApiProjectsProjectIdShiftReportsCompletionsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsCompletions';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsCompletionsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsCompletionsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/completions', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsCompletionsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
