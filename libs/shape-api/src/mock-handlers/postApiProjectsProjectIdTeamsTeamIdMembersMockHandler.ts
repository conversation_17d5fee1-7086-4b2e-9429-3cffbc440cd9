/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdMembersMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdMembersSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdMembersMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdMembers';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdMembersMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdMembersMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/members', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdMembersMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
