/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdCompleteMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdCompleteSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdCompleteMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdComplete';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdCompleteMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdCompleteMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/complete', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdCompleteMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
