/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdTeamsTeamIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdTeamsTeamIdSchema';
import { createPatchApiProjectsProjectIdTeamsTeamIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdTeamsTeamId';
import { http } from 'msw';

export function patchApiProjectsProjectIdTeamsTeamIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdTeamsTeamIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/teams/:team_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiProjectsProjectIdTeamsTeamIdMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
