/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdStatusStatementsQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdStatusStatementsSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdStatusStatementsQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdStatusStatements';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdStatusStatementsMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdStatusStatementsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/status_statements', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdStatusStatementsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
