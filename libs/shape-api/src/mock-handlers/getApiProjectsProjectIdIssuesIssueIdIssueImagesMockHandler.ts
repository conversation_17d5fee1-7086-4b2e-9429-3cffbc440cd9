/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdIssueImagesSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdIssueImages';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdIssueImagesMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/issue_images', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
