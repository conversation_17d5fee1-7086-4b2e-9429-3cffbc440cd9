/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponseSchema } from '../types/deleteApiProjectsProjectIdTeamsTeamIdJoinTokenSchema';
import { createDeleteApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponse } from '../factories/createDeleteApiProjectsProjectIdTeamsTeamIdJoinToken';
import { http } from 'msw';

export function deleteApiProjectsProjectIdTeamsTeamIdJoinTokenMockHandler(
  data?:
    | DeleteApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/teams/:team_id/join_token', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
