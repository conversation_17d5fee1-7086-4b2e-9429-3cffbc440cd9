/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdGroupsGroupIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdGroupsGroupIdSchema';
import { createPatchApiProjectsProjectIdGroupsGroupIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdGroupsGroupId';
import { http } from 'msw';

export function patchApiProjectsProjectIdGroupsGroupIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdGroupsGroupIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/groups/:group_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiProjectsProjectIdGroupsGroupIdMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
