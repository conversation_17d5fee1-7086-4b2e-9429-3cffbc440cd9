/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdPeopleQueryResponseSchema } from '../types/getApiProjectsProjectIdPeopleSchema';
import { createGetApiProjectsProjectIdPeopleQueryResponse } from '../factories/createGetApiProjectsProjectIdPeople';
import { http } from 'msw';

export function getApiProjectsProjectIdPeopleMockHandler(
  data?:
    | GetApiProjectsProjectIdPeopleQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/people', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdPeopleQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
