/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryResponseSchema } from '../types/getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesSchema';
import { createGetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryResponse } from '../factories/createGetApiProjectsProjectIdDashboardsDataHealthRecordsIssues';
import { http } from 'msw';

export function getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(
  data?:
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/dashboards/data_health_records/issues', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
