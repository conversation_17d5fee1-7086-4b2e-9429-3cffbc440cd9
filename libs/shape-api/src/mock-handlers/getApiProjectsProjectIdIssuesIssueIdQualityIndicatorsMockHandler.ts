/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdQualityIndicatorsSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdQualityIndicators';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdQualityIndicatorsMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/quality_indicators', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdQualityIndicatorsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
