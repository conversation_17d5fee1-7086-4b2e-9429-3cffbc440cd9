/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionSchema';
import { createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements/:requirement_id/completion',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponse(
              data
            )
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
