/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingQueryResponseSchema } from '../types/getApiProjectsProjectIdDashboardsDashboardIdEmbeddingSchema';
import { createGetApiProjectsProjectIdDashboardsDashboardIdEmbeddingQueryResponse } from '../factories/createGetApiProjectsProjectIdDashboardsDashboardIdEmbedding';
import { http } from 'msw';

export function getApiProjectsProjectIdDashboardsDashboardIdEmbeddingMockHandler(
  data?:
    | GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/dashboards/:dashboard_id/embedding', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDashboardsDashboardIdEmbeddingQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
