/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdPublishMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdPublishSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdPublishMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdPublish';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdPublishMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdPublishMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/publish', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdPublishMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
