/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDocumentsDocumentIdQueryResponseSchema } from '../types/getApiProjectsProjectIdDocumentsDocumentIdSchema';
import { createGetApiProjectsProjectIdDocumentsDocumentIdQueryResponse } from '../factories/createGetApiProjectsProjectIdDocumentsDocumentId';
import { http } from 'msw';

export function getApiProjectsProjectIdDocumentsDocumentIdMockHandler(
  data?:
    | GetApiProjectsProjectIdDocumentsDocumentIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/documents/:document_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdDocumentsDocumentIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
