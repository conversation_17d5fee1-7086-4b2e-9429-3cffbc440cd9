/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_activities/:shift_activity_id/restore', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
