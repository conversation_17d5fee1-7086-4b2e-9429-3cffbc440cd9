/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationResponseSchema } from '../types/patchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessSchema';
import { createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationResponse } from '../factories/createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness';
import { http } from 'msw';

export function patchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMockHandler(
  data?:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/shift_activities/:shift_activity_id/readiness', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationResponse(data)
      ),
      {
        status: 204,
      }
    );
  });
}
