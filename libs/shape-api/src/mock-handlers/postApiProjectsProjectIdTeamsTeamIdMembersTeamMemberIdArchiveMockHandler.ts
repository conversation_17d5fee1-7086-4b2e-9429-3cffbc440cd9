/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/members/:team_member_id/archive', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
