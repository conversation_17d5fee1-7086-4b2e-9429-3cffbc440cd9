/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdWatchingsQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdWatchingsSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdWatchingsQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdWatchings';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdWatchingsMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdWatchingsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/watchings', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdWatchingsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
