/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlans';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/weekly_work_plans', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdWeeklyWorkPlansMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
