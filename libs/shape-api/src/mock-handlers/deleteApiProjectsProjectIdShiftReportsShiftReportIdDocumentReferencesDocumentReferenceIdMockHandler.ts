/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdSchema';
import { createDeleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/document_references/:document_reference_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftReportsShiftReportIdDocumentReferencesDocumentReferenceIdMutationResponse(
              data
            )
        ),
        {
          status: 204,
        }
      );
    }
  );
}
