/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/resources_usage',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
