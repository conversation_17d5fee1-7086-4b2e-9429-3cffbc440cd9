/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/resources_usage/stats',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
