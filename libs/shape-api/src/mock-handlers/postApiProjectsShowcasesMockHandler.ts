/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsShowcasesMutationResponseSchema } from '../types/postApiProjectsShowcasesSchema';
import { createPostApiProjectsShowcasesMutationResponse } from '../factories/createPostApiProjectsShowcases';
import { http } from 'msw';

export function postApiProjectsShowcasesMockHandler(
  data?:
    | PostApiProjectsShowcasesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/showcases', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsShowcasesMutationResponse(data)), {
      status: 202,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
