/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesExportMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesExportSchema';
import { createPostApiProjectsProjectIdIssuesExportMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesExport';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesExportMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdIssuesExportMutationResponse(data)), {
      status: 202,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
