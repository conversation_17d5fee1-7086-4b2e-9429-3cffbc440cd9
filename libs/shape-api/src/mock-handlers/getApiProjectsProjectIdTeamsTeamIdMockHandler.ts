/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamId';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams/:team_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdTeamsTeamIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
