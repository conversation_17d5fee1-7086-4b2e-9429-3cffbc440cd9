/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsDraftQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsDraftSchema';
import { createGetApiProjectsProjectIdShiftReportsDraftQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsDraft';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsDraftMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsDraftQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/draft', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsDraftQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
