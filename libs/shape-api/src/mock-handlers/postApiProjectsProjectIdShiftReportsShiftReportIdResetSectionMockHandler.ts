/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdResetSectionSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdResetSection';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/reset_section', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
