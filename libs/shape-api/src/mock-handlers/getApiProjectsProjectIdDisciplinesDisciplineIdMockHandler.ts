/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDisciplinesDisciplineIdQueryResponseSchema } from '../types/getApiProjectsProjectIdDisciplinesDisciplineIdSchema';
import { createGetApiProjectsProjectIdDisciplinesDisciplineIdQueryResponse } from '../factories/createGetApiProjectsProjectIdDisciplinesDisciplineId';
import { http } from 'msw';

export function getApiProjectsProjectIdDisciplinesDisciplineIdMockHandler(
  data?:
    | GetApiProjectsProjectIdDisciplinesDisciplineIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/disciplines/:discipline_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDisciplinesDisciplineIdQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
