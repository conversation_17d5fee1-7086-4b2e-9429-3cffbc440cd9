/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiOrgsOrgIdMutationResponseSchema } from '../types/patchApiOrgsOrgIdSchema';
import { createPatchApiOrgsOrgIdMutationResponse } from '../factories/createPatchApiOrgsOrgId';
import { http } from 'msw';

export function patchApiOrgsOrgIdMockHandler(
  data?: PatchApiOrgsOrgIdMutationResponseSchema | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/orgs/:org_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiOrgsOrgIdMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
