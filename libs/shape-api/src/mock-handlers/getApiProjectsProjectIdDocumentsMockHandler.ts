/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdDocumentsSchema';
import { createGetApiProjectsProjectIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdDocumentsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
