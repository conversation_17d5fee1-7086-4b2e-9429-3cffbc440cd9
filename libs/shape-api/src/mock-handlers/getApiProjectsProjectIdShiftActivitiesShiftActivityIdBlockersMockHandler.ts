/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_activities/:shift_activity_id/blockers', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
