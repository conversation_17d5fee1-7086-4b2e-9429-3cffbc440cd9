/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesGroupCountQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesGroupCountSchema';
import { createGetApiProjectsProjectIdIssuesGroupCountQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesGroupCount';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesGroupCountMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesGroupCountQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/group_count', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssuesGroupCountQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
