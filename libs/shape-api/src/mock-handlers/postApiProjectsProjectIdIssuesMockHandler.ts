/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesSchema';
import { createPostApiProjectsProjectIdIssuesMutationResponse } from '../factories/createPostApiProjectsProjectIdIssues';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdIssuesMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
