/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationResponseSchema } from '../types/postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectSchema';
import { createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationResponse } from '../factories/createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect';
import { http } from 'msw';

export function postApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMockHandler(
  data?:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/access_requests/:project_access_request_id/redirect',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
