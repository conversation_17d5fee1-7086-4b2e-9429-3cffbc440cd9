/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsInReviewSchema';
import { createGetApiProjectsProjectIdShiftReportsInReviewQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsInReview';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsInReviewMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/in_review', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsInReviewQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
