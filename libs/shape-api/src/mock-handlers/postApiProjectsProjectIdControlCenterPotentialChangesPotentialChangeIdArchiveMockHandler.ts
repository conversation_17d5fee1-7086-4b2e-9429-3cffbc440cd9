/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveSchema';
import { createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id/archive',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
