/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdGroupsGroupIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdGroupsGroupIdSchema';
import { createDeleteApiProjectsProjectIdGroupsGroupIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdGroupsGroupId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdGroupsGroupIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdGroupsGroupIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/groups/:group_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createDeleteApiProjectsProjectIdGroupsGroupIdMutationResponse(data)), {
      status: 204,
    });
  });
}
