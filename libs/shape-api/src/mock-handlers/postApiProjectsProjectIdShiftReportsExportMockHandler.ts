/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsExportMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsExportSchema';
import { createPostApiProjectsProjectIdShiftReportsExportMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsExport';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsExportMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsExportMutationResponse(data)),
      {
        status: 202,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
