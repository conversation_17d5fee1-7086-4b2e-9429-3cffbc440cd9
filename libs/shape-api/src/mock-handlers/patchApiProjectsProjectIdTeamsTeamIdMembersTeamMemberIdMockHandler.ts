/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdSchema';
import { createPatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId';
import { http } from 'msw';

export function patchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/teams/:team_id/members/:team_member_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
