/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdDocumentsSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdDocumentsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
