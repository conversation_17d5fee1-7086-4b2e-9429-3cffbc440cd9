/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiTeamMembersMutationResponseSchema } from '../types/postApiTeamMembersSchema';
import { createPostApiTeamMembersMutationResponse } from '../factories/createPostApiTeamMembers';
import { http } from 'msw';

export function postApiTeamMembersMockHandler(
  data?: PostApiTeamMembersMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/team_members', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiTeamMembersMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
