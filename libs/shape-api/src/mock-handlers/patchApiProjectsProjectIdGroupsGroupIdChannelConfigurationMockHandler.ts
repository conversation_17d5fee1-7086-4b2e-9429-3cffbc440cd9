/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationResponseSchema } from '../types/patchApiProjectsProjectIdGroupsGroupIdChannelConfigurationSchema';
import { createPatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationResponse } from '../factories/createPatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration';
import { http } from 'msw';

export function patchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler(
  data?:
    | PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/groups/:group_id/channel_configuration', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
