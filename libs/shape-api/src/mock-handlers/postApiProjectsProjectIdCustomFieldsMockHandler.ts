/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdCustomFieldsMutationResponseSchema } from '../types/postApiProjectsProjectIdCustomFieldsSchema';
import { createPostApiProjectsProjectIdCustomFieldsMutationResponse } from '../factories/createPostApiProjectsProjectIdCustomFields';
import { http } from 'msw';

export function postApiProjectsProjectIdCustomFieldsMockHandler(
  data?:
    | PostApiProjectsProjectIdCustomFieldsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/custom_fields', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdCustomFieldsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
