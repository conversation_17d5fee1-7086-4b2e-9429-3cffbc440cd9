/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdLocationsLocationIdSortMutationResponseSchema } from '../types/postApiProjectsProjectIdLocationsLocationIdSortSchema';
import { createPostApiProjectsProjectIdLocationsLocationIdSortMutationResponse } from '../factories/createPostApiProjectsProjectIdLocationsLocationIdSort';
import { http } from 'msw';

export function postApiProjectsProjectIdLocationsLocationIdSortMockHandler(
  data?:
    | PostApiProjectsProjectIdLocationsLocationIdSortMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/locations/:location_id/sort', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdLocationsLocationIdSortMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
