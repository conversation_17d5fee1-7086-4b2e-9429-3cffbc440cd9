/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdChannelConfigurationSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamIdChannelConfiguration';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams/:team_id/channel_configuration', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
