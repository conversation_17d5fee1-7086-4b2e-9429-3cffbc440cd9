/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdSchema';
import { createDeleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/comments/:comment_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createDeleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
