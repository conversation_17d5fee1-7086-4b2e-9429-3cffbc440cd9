/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesSchema';
import { createGetApiProjectsProjectIdShiftActivitiesQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivities';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_activities', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdShiftActivitiesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
