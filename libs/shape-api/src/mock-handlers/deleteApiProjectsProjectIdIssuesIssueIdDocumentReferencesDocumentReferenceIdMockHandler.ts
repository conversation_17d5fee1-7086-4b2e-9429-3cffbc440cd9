/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/issues/:issue_id/document_references/:document_reference_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdIssuesIssueIdDocumentReferencesDocumentReferenceIdMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
