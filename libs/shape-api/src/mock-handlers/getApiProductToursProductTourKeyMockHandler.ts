/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProductToursProductTourKeyQueryResponseSchema } from '../types/getApiProductToursProductTourKeySchema';
import { createGetApiProductToursProductTourKeyQueryResponse } from '../factories/createGetApiProductToursProductTourKey';
import { http } from 'msw';

export function getApiProductToursProductTourKeyMockHandler(
  data?:
    | GetApiProductToursProductTourKeyQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/product_tours/:product_tour_key', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProductToursProductTourKeyQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
