/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiQueuedTasksQueryResponseSchema } from '../types/getApiQueuedTasksSchema';
import { createGetApiQueuedTasksQueryResponse } from '../factories/createGetApiQueuedTasks';
import { http } from 'msw';

export function getApiQueuedTasksMockHandler(
  data?: GetApiQueuedTasksQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/queued_tasks', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiQueuedTasksQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
