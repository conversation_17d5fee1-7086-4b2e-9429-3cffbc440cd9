/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiFeedbacksMutationResponseSchema } from '../types/postApiFeedbacksSchema';
import { createPostApiFeedbacksMutationResponse } from '../factories/createPostApiFeedbacks';
import { http } from 'msw';

export function postApiFeedbacksMockHandler(
  data?: PostApiFeedbacksMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/feedbacks', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiFeedbacksMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
