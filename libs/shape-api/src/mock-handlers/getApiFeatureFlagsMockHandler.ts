/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiFeatureFlagsQueryResponseSchema } from '../types/getApiFeatureFlagsSchema';
import { createGetApiFeatureFlagsQueryResponse } from '../factories/createGetApiFeatureFlags';
import { http } from 'msw';

export function getApiFeatureFlagsMockHandler(
  data?: GetApiFeatureFlagsQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/feature_flags', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiFeatureFlagsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
