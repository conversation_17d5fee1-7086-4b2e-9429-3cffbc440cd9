/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdLocationsQueryResponseSchema } from '../types/getApiProjectsProjectIdLocationsSchema';
import { createGetApiProjectsProjectIdLocationsQueryResponse } from '../factories/createGetApiProjectsProjectIdLocations';
import { http } from 'msw';

export function getApiProjectsProjectIdLocationsMockHandler(
  data?:
    | GetApiProjectsProjectIdLocationsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/locations', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdLocationsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
