/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdDefaultMutationResponseSchema } from '../types/postApiProjectsProjectIdDefaultSchema';
import { createPostApiProjectsProjectIdDefaultMutationResponse } from '../factories/createPostApiProjectsProjectIdDefault';
import { http } from 'msw';

export function postApiProjectsProjectIdDefaultMockHandler(
  data?:
    | PostApiProjectsProjectIdDefaultMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/default', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdDefaultMutationResponse(data)), {
      status: 204,
    });
  });
}
