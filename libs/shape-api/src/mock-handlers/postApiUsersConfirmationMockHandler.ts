/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiUsersConfirmationMutationResponseSchema } from '../types/postApiUsersConfirmationSchema';
import { createPostApiUsersConfirmationMutationResponse } from '../factories/createPostApiUsersConfirmation';
import { http } from 'msw';

export function postApiUsersConfirmationMockHandler(
  data?:
    | PostApiUsersConfirmationMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/users/confirmation', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiUsersConfirmationMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
