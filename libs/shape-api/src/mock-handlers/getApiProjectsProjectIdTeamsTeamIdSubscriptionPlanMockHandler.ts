/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams/:team_id/subscription_plan', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
