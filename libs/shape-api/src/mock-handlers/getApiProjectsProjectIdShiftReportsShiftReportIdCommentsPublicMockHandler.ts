/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/:shift_report_id/comments/public', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
