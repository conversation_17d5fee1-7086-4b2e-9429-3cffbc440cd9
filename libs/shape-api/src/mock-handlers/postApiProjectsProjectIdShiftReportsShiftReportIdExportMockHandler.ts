/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdExportMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdExportSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdExportMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdExport';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdExportMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdExportMutationResponse(data)),
      {
        status: 202,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
