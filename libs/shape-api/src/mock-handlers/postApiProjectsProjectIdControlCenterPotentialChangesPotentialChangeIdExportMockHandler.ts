/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMutationResponseSchema } from '../types/postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportSchema';
import { createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMutationResponse } from '../factories/createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport';
import { http } from 'msw';

export function postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMockHandler(
  data?:
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id/export',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMutationResponse(data)
        ),
        {
          status: 202,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
