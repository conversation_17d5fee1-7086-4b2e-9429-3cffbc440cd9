/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReview';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/submit_for_review',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftReportsShiftReportIdSubmitForReviewMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
