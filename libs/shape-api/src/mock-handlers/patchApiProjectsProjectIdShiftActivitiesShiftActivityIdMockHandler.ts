/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdShiftActivitiesShiftActivityIdSchema';
import { createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdShiftActivitiesShiftActivityId';
import { http } from 'msw';

export function patchApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/shift_activities/:shift_activity_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
