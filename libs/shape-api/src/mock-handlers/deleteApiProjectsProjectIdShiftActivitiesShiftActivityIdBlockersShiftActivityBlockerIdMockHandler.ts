/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdSchema';
import { createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/blockers/:shift_activity_blocker_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersShiftActivityBlockerIdMutationResponse(
              data
            )
        ),
        {
          status: 204,
        }
      );
    }
  );
}
