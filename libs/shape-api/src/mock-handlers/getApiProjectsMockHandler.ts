/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsQueryResponseSchema } from '../types/getApiProjectsSchema';
import { createGetApiProjectsQueryResponse } from '../factories/createGetApiProjects';
import { http } from 'msw';

export function getApiProjectsMockHandler(
  data?: GetApiProjectsQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
