/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdAccessRequestsMutationResponseSchema } from '../types/postApiProjectsProjectIdAccessRequestsSchema';
import { createPostApiProjectsProjectIdAccessRequestsMutationResponse } from '../factories/createPostApiProjectsProjectIdAccessRequests';
import { http } from 'msw';

export function postApiProjectsProjectIdAccessRequestsMockHandler(
  data?:
    | PostApiProjectsProjectIdAccessRequestsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/access_requests', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdAccessRequestsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
