/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsSchema';
import { createGetApiProjectsProjectIdShiftReportsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReports';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
