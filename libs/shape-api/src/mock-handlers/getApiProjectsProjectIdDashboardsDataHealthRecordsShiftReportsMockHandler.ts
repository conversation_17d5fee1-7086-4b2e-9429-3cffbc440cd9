/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryResponseSchema } from '../types/getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsSchema';
import { createGetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryResponse } from '../factories/createGetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports';
import { http } from 'msw';

export function getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(
  data?:
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/dashboards/data_health_records/shift_reports', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
