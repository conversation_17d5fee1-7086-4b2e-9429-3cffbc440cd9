/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdDocumentsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdDocumentsSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdDocumentsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdDocuments';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdDocumentsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdDocumentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdDocumentsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
