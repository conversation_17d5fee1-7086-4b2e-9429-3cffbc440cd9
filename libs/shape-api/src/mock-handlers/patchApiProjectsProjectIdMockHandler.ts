/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdSchema';
import { createPatchApiProjectsProjectIdMutationResponse } from '../factories/createPatchApiProjectsProjectId';
import { http } from 'msw';

export function patchApiProjectsProjectIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiProjectsProjectIdMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
