/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/prefill', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationResponse(data)
      ),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
