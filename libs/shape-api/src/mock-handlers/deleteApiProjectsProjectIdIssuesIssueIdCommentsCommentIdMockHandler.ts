/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueIdCommentsCommentId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/issues/:issue_id/comments/:comment_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
