/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/teams/:team_id/members/:team_member_id/issue_dependencies',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
