/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationResponseSchema } from '../types/patchApiProjectsProjectIdIssuesIssueIdCustomFieldsSchema';
import { createPatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationResponse } from '../factories/createPatchApiProjectsProjectIdIssuesIssueIdCustomFields';
import { http } from 'msw';

export function patchApiProjectsProjectIdIssuesIssueIdCustomFieldsMockHandler(
  data?:
    | PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/issues/:issue_id/custom_fields', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
