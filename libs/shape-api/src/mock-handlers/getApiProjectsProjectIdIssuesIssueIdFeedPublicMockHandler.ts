/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdFeedPublicSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdFeedPublic';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdFeedPublicMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/feed/public', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
