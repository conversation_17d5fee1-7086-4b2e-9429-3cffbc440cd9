/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiOrgsCheckDomainMutationResponseSchema } from '../types/postApiOrgsCheckDomainSchema';
import { createPostApiOrgsCheckDomainMutationResponse } from '../factories/createPostApiOrgsCheckDomain';
import { http } from 'msw';

export function postApiOrgsCheckDomainMockHandler(
  data?:
    | PostApiOrgsCheckDomainMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/orgs/check_domain', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiOrgsCheckDomainMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
