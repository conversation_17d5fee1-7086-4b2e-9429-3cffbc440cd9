/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdJoinTokenSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdJoinToken';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdJoinTokenMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/join_token', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
