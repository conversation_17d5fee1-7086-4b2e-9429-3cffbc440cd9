/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiChannelsTokenMutationResponseSchema } from '../types/postApiChannelsTokenSchema';
import { createPostApiChannelsTokenMutationResponse } from '../factories/createPostApiChannelsToken';
import { http } from 'msw';

export function postApiChannelsTokenMockHandler(
  data?:
    | PostApiChannelsTokenMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/channels/token', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiChannelsTokenMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
