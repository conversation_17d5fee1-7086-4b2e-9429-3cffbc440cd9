/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/comments/public', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationResponse(data)
      ),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
