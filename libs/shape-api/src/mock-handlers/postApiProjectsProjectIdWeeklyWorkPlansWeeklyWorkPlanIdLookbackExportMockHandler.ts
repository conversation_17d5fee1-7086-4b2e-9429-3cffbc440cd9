/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/lookback/export',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMutationResponse(data)
        ),
        {
          status: 202,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
