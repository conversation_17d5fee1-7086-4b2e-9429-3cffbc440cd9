/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssueViewsQueryResponseSchema } from '../types/getApiProjectsProjectIdIssueViewsSchema';
import { createGetApiProjectsProjectIdIssueViewsQueryResponse } from '../factories/createGetApiProjectsProjectIdIssueViews';
import { http } from 'msw';

export function getApiProjectsProjectIdIssueViewsMockHandler(
  data?:
    | GetApiProjectsProjectIdIssueViewsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issue_views', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssueViewsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
