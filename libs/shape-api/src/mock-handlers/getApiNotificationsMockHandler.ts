/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiNotificationsQueryResponseSchema } from '../types/getApiNotificationsSchema';
import { createGetApiNotificationsQueryResponse } from '../factories/createGetApiNotifications';
import { http } from 'msw';

export function getApiNotificationsMockHandler(
  data?: GetApiNotificationsQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/notifications', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiNotificationsQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
