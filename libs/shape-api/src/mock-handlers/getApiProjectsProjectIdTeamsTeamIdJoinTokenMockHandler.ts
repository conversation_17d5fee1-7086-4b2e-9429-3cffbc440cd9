/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdTeamsTeamIdJoinTokenQueryResponseSchema } from '../types/getApiProjectsProjectIdTeamsTeamIdJoinTokenSchema';
import { createGetApiProjectsProjectIdTeamsTeamIdJoinTokenQueryResponse } from '../factories/createGetApiProjectsProjectIdTeamsTeamIdJoinToken';
import { http } from 'msw';

export function getApiProjectsProjectIdTeamsTeamIdJoinTokenMockHandler(
  data?:
    | GetApiProjectsProjectIdTeamsTeamIdJoinTokenQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/teams/:team_id/join_token', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdTeamsTeamIdJoinTokenQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
