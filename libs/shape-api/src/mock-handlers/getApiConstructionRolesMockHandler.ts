/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiConstructionRolesQueryResponseSchema } from '../types/getApiConstructionRolesSchema';
import { createGetApiConstructionRolesQueryResponse } from '../factories/createGetApiConstructionRoles';
import { http } from 'msw';

export function getApiConstructionRolesMockHandler(
  data?:
    | GetApiConstructionRolesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/construction_roles', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiConstructionRolesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
