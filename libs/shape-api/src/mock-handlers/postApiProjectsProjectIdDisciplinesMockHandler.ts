/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdDisciplinesMutationResponseSchema } from '../types/postApiProjectsProjectIdDisciplinesSchema';
import { createPostApiProjectsProjectIdDisciplinesMutationResponse } from '../factories/createPostApiProjectsProjectIdDisciplines';
import { http } from 'msw';

export function postApiProjectsProjectIdDisciplinesMockHandler(
  data?:
    | PostApiProjectsProjectIdDisciplinesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/disciplines', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdDisciplinesMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
