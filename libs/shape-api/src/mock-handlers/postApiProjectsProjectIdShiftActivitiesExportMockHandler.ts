/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesExportMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesExportSchema';
import { createPostApiProjectsProjectIdShiftActivitiesExportMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesExport';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesExportMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_activities/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftActivitiesExportMutationResponse(data)),
      {
        status: 202,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
