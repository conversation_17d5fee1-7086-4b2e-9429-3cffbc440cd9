/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdApproveMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdApproveSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdApproveMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdApprove';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdApproveMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdApproveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/approve', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdApproveMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
