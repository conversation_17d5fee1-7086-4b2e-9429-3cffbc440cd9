/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiKnowledgeBaseCategoriesQueryResponseSchema } from '../types/getApiKnowledgeBaseCategoriesSchema';
import { createGetApiKnowledgeBaseCategoriesQueryResponse } from '../factories/createGetApiKnowledgeBaseCategories';
import { http } from 'msw';

export function getApiKnowledgeBaseCategoriesMockHandler(
  data?:
    | GetApiKnowledgeBaseCategoriesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/knowledge_base/categories', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiKnowledgeBaseCategoriesQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
