/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdArchiveSchema';
import { createPostApiProjectsProjectIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/archive', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdArchiveMutationResponse(data)), {
      status: 200,
    });
  });
}
