/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdImportSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdImport';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdImportMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/import', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
