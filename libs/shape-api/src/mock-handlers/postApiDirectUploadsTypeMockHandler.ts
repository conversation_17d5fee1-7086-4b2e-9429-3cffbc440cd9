/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiDirectUploadsTypeMutationResponseSchema } from '../types/postApiDirectUploadsTypeSchema';
import { createPostApiDirectUploadsTypeMutationResponse } from '../factories/createPostApiDirectUploadsType';
import { http } from 'msw';

export function postApiDirectUploadsTypeMockHandler(
  data?:
    | PostApiDirectUploadsTypeMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/direct_uploads/:type', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiDirectUploadsTypeMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
