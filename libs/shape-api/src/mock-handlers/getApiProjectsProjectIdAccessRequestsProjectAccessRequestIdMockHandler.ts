/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdQueryResponseSchema } from '../types/getApiProjectsProjectIdAccessRequestsProjectAccessRequestIdSchema';
import { createGetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdQueryResponse } from '../factories/createGetApiProjectsProjectIdAccessRequestsProjectAccessRequestId';
import { http } from 'msw';

export function getApiProjectsProjectIdAccessRequestsProjectAccessRequestIdMockHandler(
  data?:
    | GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/access_requests/:project_access_request_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
