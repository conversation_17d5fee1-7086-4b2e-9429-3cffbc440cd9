/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdVisitMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdVisitSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdVisitMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdVisit';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdVisitMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdVisitMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/visit', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdVisitMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
