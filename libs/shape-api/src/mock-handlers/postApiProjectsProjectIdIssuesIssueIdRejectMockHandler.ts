/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdRejectSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdRejectMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdReject';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdRejectMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/reject', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdRejectMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
