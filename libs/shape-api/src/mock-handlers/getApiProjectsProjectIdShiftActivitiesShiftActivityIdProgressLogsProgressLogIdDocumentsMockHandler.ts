/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs/:progress_log_id/documents',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryResponse(
              data
            )
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
