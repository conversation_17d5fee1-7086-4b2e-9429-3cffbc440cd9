/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryResponseSchema } from '../types/getApiProjectsProjectIdControlCenterChangeSignalsDowntimesSchema';
import { createGetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryResponse } from '../factories/createGetApiProjectsProjectIdControlCenterChangeSignalsDowntimes';
import { http } from 'msw';

export function getApiProjectsProjectIdControlCenterChangeSignalsDowntimesMockHandler(
  data?:
    | GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/control_center/change_signals/downtimes', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
