/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiLoginMutationResponseSchema } from '../types/postApiLoginSchema';
import { createPostApiLoginMutationResponse } from '../factories/createPostApiLogin';
import { http } from 'msw';

export function postApiLoginMockHandler(
  data?: PostApiLoginMutationResponseSchema | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/login', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiLoginMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
