/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
