/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdDisciplinesDisciplineIdSchema';
import { createPatchApiProjectsProjectIdDisciplinesDisciplineIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdDisciplinesDisciplineId';
import { http } from 'msw';

export function patchApiProjectsProjectIdDisciplinesDisciplineIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/disciplines/:discipline_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdDisciplinesDisciplineIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
