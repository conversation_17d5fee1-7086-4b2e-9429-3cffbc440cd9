/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdPeopleSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdPeople';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdPeopleMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_reports/:shift_report_id/people', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
