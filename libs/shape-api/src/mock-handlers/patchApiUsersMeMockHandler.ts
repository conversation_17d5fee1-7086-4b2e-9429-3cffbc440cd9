/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiUsersMeMutationResponseSchema } from '../types/patchApiUsersMeSchema';
import { createPatchApiUsersMeMutationResponse } from '../factories/createPatchApiUsersMe';
import { http } from 'msw';

export function patchApiUsersMeMockHandler(
  data?: PatchApiUsersMeMutationResponseSchema | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/users/me', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiUsersMeMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
