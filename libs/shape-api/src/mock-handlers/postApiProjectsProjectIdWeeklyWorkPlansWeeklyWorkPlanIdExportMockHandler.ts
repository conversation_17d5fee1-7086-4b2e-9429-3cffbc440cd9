/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMutationResponseSchema } from '../types/postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportSchema';
import { createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMutationResponse } from '../factories/createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport';
import { http } from 'msw';

export function postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMockHandler(
  data?:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/weekly_work_plans/:weekly_work_plan_id/export', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMutationResponse(data)),
      {
        status: 202,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
