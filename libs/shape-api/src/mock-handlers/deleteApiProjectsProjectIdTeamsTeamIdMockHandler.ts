/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdTeamsTeamIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdTeamsTeamIdSchema';
import { createDeleteApiProjectsProjectIdTeamsTeamIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdTeamsTeamId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdTeamsTeamIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdTeamsTeamIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/teams/:team_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createDeleteApiProjectsProjectIdTeamsTeamIdMutationResponse(data)), {
      status: 204,
    });
  });
}
