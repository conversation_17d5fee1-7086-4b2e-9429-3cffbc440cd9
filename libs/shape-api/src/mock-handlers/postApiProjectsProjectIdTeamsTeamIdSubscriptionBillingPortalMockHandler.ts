/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/subscription/billing_portal', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
