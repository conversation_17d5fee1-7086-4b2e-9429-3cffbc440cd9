/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdLocationsLocationIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdLocationsLocationIdSchema';
import { createPatchApiProjectsProjectIdLocationsLocationIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdLocationsLocationId';
import { http } from 'msw';

export function patchApiProjectsProjectIdLocationsLocationIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdLocationsLocationIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/locations/:location_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdLocationsLocationIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
