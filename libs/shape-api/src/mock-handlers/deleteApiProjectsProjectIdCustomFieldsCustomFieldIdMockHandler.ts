/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdCustomFieldsCustomFieldIdSchema';
import { createDeleteApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdCustomFieldsCustomFieldId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/projects/:project_id/custom_fields/:custom_field_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
