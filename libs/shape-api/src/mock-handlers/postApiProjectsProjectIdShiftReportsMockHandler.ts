/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsSchema';
import { createPostApiProjectsProjectIdShiftReportsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReports';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
