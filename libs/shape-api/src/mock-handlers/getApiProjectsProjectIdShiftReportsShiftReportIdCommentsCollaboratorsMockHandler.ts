/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/comments/collaborators',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
