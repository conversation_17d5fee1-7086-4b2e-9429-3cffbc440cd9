/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdCommentsMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdCommentsSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdCommentsMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdComments';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdCommentsMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdCommentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/comments', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdCommentsMutationResponse(data)),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
