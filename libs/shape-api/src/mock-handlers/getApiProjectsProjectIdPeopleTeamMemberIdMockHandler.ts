/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdPeopleTeamMemberIdQueryResponseSchema } from '../types/getApiProjectsProjectIdPeopleTeamMemberIdSchema';
import { createGetApiProjectsProjectIdPeopleTeamMemberIdQueryResponse } from '../factories/createGetApiProjectsProjectIdPeopleTeamMemberId';
import { http } from 'msw';

export function getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(
  data?:
    | GetApiProjectsProjectIdPeopleTeamMemberIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/people/:team_member_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdPeopleTeamMemberIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
