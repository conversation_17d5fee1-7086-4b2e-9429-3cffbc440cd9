/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/:resource_type/:resource_id/documents',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createGetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
