/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdSchema';
import { createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId';
import { http } from 'msw';

export function patchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/requirements/:requirement_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createPatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
