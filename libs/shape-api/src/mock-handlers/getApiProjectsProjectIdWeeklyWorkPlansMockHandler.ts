/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdWeeklyWorkPlansQueryResponseSchema } from '../types/getApiProjectsProjectIdWeeklyWorkPlansSchema';
import { createGetApiProjectsProjectIdWeeklyWorkPlansQueryResponse } from '../factories/createGetApiProjectsProjectIdWeeklyWorkPlans';
import { http } from 'msw';

export function getApiProjectsProjectIdWeeklyWorkPlansMockHandler(
  data?:
    | GetApiProjectsProjectIdWeeklyWorkPlansQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/weekly_work_plans', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdWeeklyWorkPlansQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
