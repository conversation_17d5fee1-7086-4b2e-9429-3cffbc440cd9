/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityId';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_activities/:shift_activity_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
