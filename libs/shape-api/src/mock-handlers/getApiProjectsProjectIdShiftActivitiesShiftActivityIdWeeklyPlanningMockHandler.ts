/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/weekly_planning',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
