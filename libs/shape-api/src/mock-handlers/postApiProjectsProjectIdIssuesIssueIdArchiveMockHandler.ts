/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdIssuesIssueIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdIssuesIssueIdArchiveSchema';
import { createPostApiProjectsProjectIdIssuesIssueIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdIssuesIssueIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdIssuesIssueIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdIssuesIssueIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/issues/:issue_id/archive', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdIssuesIssueIdArchiveMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
