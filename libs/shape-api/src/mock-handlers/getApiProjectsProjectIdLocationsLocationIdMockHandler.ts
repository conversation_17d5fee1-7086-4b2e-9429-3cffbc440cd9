/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdLocationsLocationIdQueryResponseSchema } from '../types/getApiProjectsProjectIdLocationsLocationIdSchema';
import { createGetApiProjectsProjectIdLocationsLocationIdQueryResponse } from '../factories/createGetApiProjectsProjectIdLocationsLocationId';
import { http } from 'msw';

export function getApiProjectsProjectIdLocationsLocationIdMockHandler(
  data?:
    | GetApiProjectsProjectIdLocationsLocationIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/locations/:location_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdLocationsLocationIdQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
