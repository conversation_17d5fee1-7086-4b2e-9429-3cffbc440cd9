/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiOnboardingQueryResponseSchema } from '../types/getApiOnboardingSchema';
import { createGetApiOnboardingQueryResponse } from '../factories/createGetApiOnboarding';
import { http } from 'msw';

export function getApiOnboardingMockHandler(
  data?: GetApiOnboardingQueryResponseSchema | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/onboarding', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiOnboardingQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
