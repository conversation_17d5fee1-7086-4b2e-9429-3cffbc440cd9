/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/shift_reports/:shift_report_id/resources/:kind', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationResponse(data)
      ),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
