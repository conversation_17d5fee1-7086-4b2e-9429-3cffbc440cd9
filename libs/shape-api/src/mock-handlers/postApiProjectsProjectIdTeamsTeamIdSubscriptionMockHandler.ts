/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdSubscriptionSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdSubscription';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdSubscriptionMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/teams/:team_id/subscription', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
