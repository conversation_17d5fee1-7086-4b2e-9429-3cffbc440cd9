/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiTeamJoinTokensTokenQueryResponseSchema } from '../types/getApiTeamJoinTokensTokenSchema';
import { createGetApiTeamJoinTokensTokenQueryResponse } from '../factories/createGetApiTeamJoinTokensToken';
import { http } from 'msw';

export function getApiTeamJoinTokensTokenMockHandler(
  data?:
    | GetApiTeamJoinTokensTokenQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/team_join_tokens/:token', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiTeamJoinTokensTokenQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
