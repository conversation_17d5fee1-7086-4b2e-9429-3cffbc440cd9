/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdIssuesIssueIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdIssuesIssueIdSchema';
import { createPatchApiProjectsProjectIdIssuesIssueIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdIssuesIssueId';
import { http } from 'msw';

export function patchApiProjectsProjectIdIssuesIssueIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdIssuesIssueIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/issues/:issue_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiProjectsProjectIdIssuesIssueIdMutationResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
