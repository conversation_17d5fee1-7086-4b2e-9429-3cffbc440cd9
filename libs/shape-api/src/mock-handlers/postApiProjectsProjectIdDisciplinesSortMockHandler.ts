/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdDisciplinesSortMutationResponseSchema } from '../types/postApiProjectsProjectIdDisciplinesSortSchema';
import { createPostApiProjectsProjectIdDisciplinesSortMutationResponse } from '../factories/createPostApiProjectsProjectIdDisciplinesSort';
import { http } from 'msw';

export function postApiProjectsProjectIdDisciplinesSortMockHandler(
  data?:
    | PostApiProjectsProjectIdDisciplinesSortMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/disciplines/sort', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdDisciplinesSortMutationResponse(data)), {
      status: 204,
    });
  });
}
