/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdDocumentsMutationResponseSchema } from '../types/postApiProjectsProjectIdDocumentsSchema';
import { createPostApiProjectsProjectIdDocumentsMutationResponse } from '../factories/createPostApiProjectsProjectIdDocuments';
import { http } from 'msw';

export function postApiProjectsProjectIdDocumentsMockHandler(
  data?:
    | PostApiProjectsProjectIdDocumentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('*/api/projects/:project_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiProjectsProjectIdDocumentsMutationResponse(data)), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
