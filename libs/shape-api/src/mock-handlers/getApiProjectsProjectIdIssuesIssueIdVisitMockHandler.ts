/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdIssuesIssueIdVisitQueryResponseSchema } from '../types/getApiProjectsProjectIdIssuesIssueIdVisitSchema';
import { createGetApiProjectsProjectIdIssuesIssueIdVisitQueryResponse } from '../factories/createGetApiProjectsProjectIdIssuesIssueIdVisit';
import { http } from 'msw';

export function getApiProjectsProjectIdIssuesIssueIdVisitMockHandler(
  data?:
    | GetApiProjectsProjectIdIssuesIssueIdVisitQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/issues/:issue_id/visit', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createGetApiProjectsProjectIdIssuesIssueIdVisitQueryResponse(data)), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });
}
