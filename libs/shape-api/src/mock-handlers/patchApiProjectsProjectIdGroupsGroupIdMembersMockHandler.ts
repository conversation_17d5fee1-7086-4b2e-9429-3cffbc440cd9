/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdGroupsGroupIdMembersMutationResponseSchema } from '../types/patchApiProjectsProjectIdGroupsGroupIdMembersSchema';
import { createPatchApiProjectsProjectIdGroupsGroupIdMembersMutationResponse } from '../factories/createPatchApiProjectsProjectIdGroupsGroupIdMembers';
import { http } from 'msw';

export function patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler(
  data?:
    | PatchApiProjectsProjectIdGroupsGroupIdMembersMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/groups/:group_id/members', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdGroupsGroupIdMembersMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
