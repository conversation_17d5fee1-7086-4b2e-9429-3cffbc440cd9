/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsSchema';
import { createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicators';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_reports/:shift_report_id/quality_indicators',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createGetApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
