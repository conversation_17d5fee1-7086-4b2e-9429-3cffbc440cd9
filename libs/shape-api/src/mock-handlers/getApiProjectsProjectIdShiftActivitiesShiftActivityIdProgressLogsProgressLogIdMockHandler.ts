/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs/:progress_log_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdQueryResponse(data)
        ),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  );
}
