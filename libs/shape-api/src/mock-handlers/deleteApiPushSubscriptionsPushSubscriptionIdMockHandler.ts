/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiPushSubscriptionsPushSubscriptionIdMutationResponseSchema } from '../types/deleteApiPushSubscriptionsPushSubscriptionIdSchema';
import { createDeleteApiPushSubscriptionsPushSubscriptionIdMutationResponse } from '../factories/createDeleteApiPushSubscriptionsPushSubscriptionId';
import { http } from 'msw';

export function deleteApiPushSubscriptionsPushSubscriptionIdMockHandler(
  data?:
    | DeleteApiPushSubscriptionsPushSubscriptionIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('*/api/push_subscriptions/:push_subscription_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiPushSubscriptionsPushSubscriptionIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
