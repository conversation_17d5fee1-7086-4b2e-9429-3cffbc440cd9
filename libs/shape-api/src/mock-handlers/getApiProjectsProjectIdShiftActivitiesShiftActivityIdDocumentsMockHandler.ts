/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryResponseSchema } from '../types/getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsSchema';
import { createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryResponse } from '../factories/createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments';
import { http } from 'msw';

export function getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsMockHandler(
  data?:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryResponseSchema
    | ((info: Parameters<Parameters<typeof http.get>[1]>[0]) => Response)
) {
  return http.get('*/api/projects/:project_id/shift_activities/:shift_activity_id/documents', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createGetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
