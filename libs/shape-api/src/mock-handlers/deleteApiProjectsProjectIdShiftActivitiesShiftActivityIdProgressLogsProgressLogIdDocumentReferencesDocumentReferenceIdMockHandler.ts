/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdSchema';
import { createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '*/api/projects/:project_id/shift_activities/:shift_activity_id/progress_logs/:progress_log_id/document_references/:document_reference_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentReferencesDocumentReferenceIdMutationResponse(
              data
            )
        ),
        {
          status: 204,
        }
      );
    }
  );
}
