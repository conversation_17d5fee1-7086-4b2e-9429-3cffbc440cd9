/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationResponseSchema } from '../types/patchApiProjectsProjectIdIssueViewsIssueViewIdSchema';
import { createPatchApiProjectsProjectIdIssueViewsIssueViewIdMutationResponse } from '../factories/createPatchApiProjectsProjectIdIssueViewsIssueViewId';
import { http } from 'msw';

export function patchApiProjectsProjectIdIssueViewsIssueViewIdMockHandler(
  data?:
    | PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('*/api/projects/:project_id/issue_views/:issue_view_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPatchApiProjectsProjectIdIssueViewsIssueViewIdMutationResponse(data)),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  });
}
