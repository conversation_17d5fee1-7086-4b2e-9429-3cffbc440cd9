// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftReportDownTimeDetailsBasicSchema } from '../types/shiftReportDownTimeDetailsBasicSchema';
import { faker } from '@faker-js/faker';

export function createShiftReportDownTimeDetailsBasic(
  data?: Partial<ShiftReportDownTimeDetailsBasicSchema>
): ShiftReportDownTimeDetailsBasicSchema {
  faker.seed([100]);
  return {
    ...{
      causalType: faker.string.alpha(),
      id: faker.string.uuid(),
      issueDescription: faker.string.alpha(),
      issueId: faker.string.uuid(),
      shiftActivityId: faker.string.uuid(),
      teamMemberId: faker.number.int(),
      timeLost: faker.number.float(),
    },
    ...(data || {}),
  };
}
