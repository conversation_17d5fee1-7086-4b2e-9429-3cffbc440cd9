// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PatchApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema,
  PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema,
  PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema,
} from '../types/patchApiProjectsProjectIdShiftReportsShiftReportIdSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createShiftReport } from './createShiftReport';
import { createShiftReportVisibility } from './createShiftReportVisibility';
import { faker } from '@faker-js/faker';

export function createPatchApiProjectsProjectIdShiftReportsShiftReportIdPathParams(
  data?: Partial<PatchApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema>
): PatchApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), shift_report_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Shift report updated
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId200() {
  faker.seed([100]);
  return createShiftReport();
}

/**
 * @description Bad request
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId400() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Authentication required
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorised
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId403() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Not found
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId404() {
  faker.seed([100]);
  return undefined;
}

/**
 * @description Update failed
 */
export function createPatchApiProjectsProjectIdShiftReportsShiftReportId422() {
  faker.seed([100]);
  return createError();
}

export function createPatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequest(
  data?: Partial<PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema>
): PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema {
  faker.seed([100]);
  return {
    ...{
      approver_id: faker.number.int(),
      client_document_reference_number: faker.string.alpha(),
      collaborators_team_member_ids: faker.helpers.multiple(() => faker.number.int()),
      contractor_logo_signed_id: faker.string.alpha(),
      contractor_name: faker.string.alpha(),
      internal_document_reference_number: faker.string.alpha(),
      notes: faker.string.alpha(),
      project_number: faker.string.alpha(),
      report_date: faker.date.anytime().toISOString().substring(0, 10),
      report_title: faker.string.alpha(),
      shift_end: faker.date.anytime().toISOString().substring(11, 19),
      shift_start: faker.date.anytime().toISOString().substring(11, 19),
      shift_type: faker.string.alpha(),
      visibility: createShiftReportVisibility(),
      visibility_specific_team_ids: faker.helpers.multiple(() => faker.string.uuid()),
      weather_description: faker.string.alpha(),
      weather_temperature: faker.string.alpha(),
      activities: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        _id: faker.string.alpha(),
        comment: faker.string.alpha(),
        description: faker.string.alpha(),
        id: faker.string.uuid(),
        location_id: faker.string.uuid(),
        planned: faker.string.alpha(),
        quantity: faker.number.float(),
        shift_activity_id: faker.string.uuid(),
        units: faker.string.alpha(),
      })),
      contract_forces: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        activities: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        comment: faker.string.alpha(),
        down_times: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        hours: faker.number.float(),
        id: faker.string.uuid(),
        organisation_resource_id: faker.string.uuid(),
        person_resource_id: faker.string.uuid(),
        role_resource_id: faker.string.uuid(),
      })),
      down_times: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        _id: faker.string.alpha(),
        causal_type: faker.string.alpha(),
        id: faker.string.uuid(),
        issue_description: faker.string.alpha(),
        issue_id: faker.string.uuid(),
        shift_activity_id: faker.string.uuid(),
        time_lost: faker.number.float(),
      })),
      equipments: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        activities: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        down_times: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        equipment_id: faker.string.alpha(),
        equipment_resource_id: faker.string.uuid(),
        hours: faker.number.float(),
        id: faker.string.uuid(),
        quantity: faker.number.float(),
      })),
      materials: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        activities: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        down_times: faker.helpers.multiple(() => ({
          _destroy: faker.string.alpha(),
          id: faker.string.uuid(),
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        id: faker.string.uuid(),
        material_resource_id: faker.string.uuid(),
        quantity: faker.number.float(),
        units: faker.string.alpha(),
      })),
      safety_health_environments: faker.helpers.multiple(() => ({
        _destroy: faker.string.alpha(),
        id: faker.string.uuid(),
        safety_note: faker.string.alpha(),
      })),
    },
    ...(data || {}),
  };
}

export function createPatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponse(
  data?: Partial<PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema>
): PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createPatchApiProjectsProjectIdShiftReportsShiftReportId200()]);
}
