import { type UserConfig, defineConfig } from '@kubb/core';
import { pluginClient } from '@kubb/plugin-client';
import { pluginMsw } from '@kubb/plugin-msw';
import { pluginOas } from '@kubb/plugin-oas';
import { pluginReactQuery } from '@kubb/plugin-react-query';
import { pluginTs } from '@kubb/plugin-ts';
import { pluginFaker } from '@kubb/plugin-faker';

export default defineConfig({
  input: {
    path: './schema.yaml',
  },
  output: {
    extension: { '.ts': '' },
    clean: true,
    path: './src',
    barrelType: false,
  },
  plugins: [
    pluginOas({
      validate: true,
      // @ts-ignore
      output: false,
    }),
    pluginTs({
      output: {
        path: './types',
      },
      exclude: [],
      enumType: 'asConst',
      enumSuffix: 'Enum',
      dateType: 'string',
      unknownType: 'unknown',
      emptySchemaType: 'void',
      optionalType: 'questionToken',
      oasType: false,
      transformers: {
        name: (name, type) => {
          if (type === 'file') return `${name[0].toLowerCase() + name.slice(1)}Schema`;
          return `${name}Schema`;
        },
      },
    }),
    pluginClient({
      importPath: '../../client',
      output: {
        path: './api/index.ts',
        barrelType: 'propagate',
        banner: `
          /**
           * Generated by kubb.
           * Do not edit manually.
           * Shape API
           */        
        `,
      },
    }),
    pluginReactQuery({
      client: {
        importPath: '../../client',
      },
      output: {
        path: './hooks/index.ts',
      },
      paramsCasing: 'camelcase',
      suspense: {},
    }),
    pluginMsw({
      output: {
        path: './mock-handlers',
      },
      baseURL: '*',
      parser: 'faker',
      transformers: {
        name: (name, _type) => {
          return name.replace(/Handler$/, 'MockHandler');
        },
      },
    }),
    pluginFaker({
      output: {
        path: './factories',
        banner: '// @ts-nocheck', // TODO: Can be removed after this is fixed -> https://github.com/kubb-labs/kubb/issues/1767
      },
      dateType: 'string',
      unknownType: 'unknown',
      emptySchemaType: 'void',
      seed: [100],
    }),
  ],
}) as UserConfig;
